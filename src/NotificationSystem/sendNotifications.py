import pymongo
import smtplib
from datetime import datetime, timedelta
import os
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

from ..helper.db import connect_db

def handler(event, context):
    try:
        # Connect to MongoDB
        db = connect_db()
        properties_collection = db['PropFromBridge']

        # Get current time and calculate the time 24 hours ago
        time_threshold = datetime.utcnow() - timedelta(hours=24)

        # Query for properties that are FASST requested and haven't been updated in the last 24 hours
        query = {
            "status.userRequest.status": "fasstRequested",
            "updatedAt": {"$lt": time_threshold}
        }

        properties = list(properties_collection.find(query))

        # Only send email if there are properties
        if properties:
            # Email content with HTML formatting and extra spacing between properties
            email_body = "The following properties have not been updated in the last 24 hours:<br><br>"
            for index, prop in enumerate(properties, start=1):
                address = f"{prop['addressStreet']}, {prop['addressCity']}, {prop['addressState']}, {prop['addressZipcode']}"
                email_body += f"{index}. {address}<br><br>"  # Double <br> for extra space between entries

            # Set up email details from environment variables
            sender_email = os.getenv('SENDER_EMAIL')  # Your Gmail address from environment variable
            app_password = os.getenv('APP_PASSWORD')  # Your Gmail app password from environment variable
            recipient_email = os.getenv('RECIPIENT_EMAIL')  # The recipient email address from environment variable

            # Create the email message
            msg = MIMEMultipart()
            msg['From'] = sender_email
            msg['To'] = recipient_email
            msg['Subject'] = f"FASST Properties Requiring Update - {datetime.utcnow().strftime('%Y-%m-%d')}"
            msg.attach(MIMEText(email_body, 'html'))  # Set content type to 'html'

            # Send email via Gmail SMTP
            try:
                with smtplib.SMTP('smtp.gmail.com', 587) as server:
                    server.starttls()  # Use TLS
                    server.login(sender_email, app_password)  # Login using your Gmail credentials
                    server.sendmail(sender_email, recipient_email, msg.as_string())  # Send email

            except Exception as email_error:
                print(f"Error sending email: {str(email_error)}")
                return {
                    'statusCode': 500,
                    'body': f"Error sending email: {str(email_error)}"
                }

            return {
                'statusCode': 200,
                'body': f"Sent notification for {len(properties)} properties."
            }

        return {
            'statusCode': 200,
            'body': "No properties require updates."
        }

    except Exception as e:
        print(f"Error sending notifications: {str(e)}")
        return {
            'statusCode': 500,
            'body': f"Error: {str(e)}"
        }
