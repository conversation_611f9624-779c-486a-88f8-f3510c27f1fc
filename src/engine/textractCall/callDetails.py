import json
import os
import traceback

from pymongo import MongoClient
from bson.objectid import ObjectId
import boto3 

textract =boto3.client('textract')
# ssm= boto3.client('ssm')


PYTHON_DB_URL=os.environ.get('PYTHON_DB_URL')
SNSTopicArn=os.environ.get('ENV_TOPIC_ARN')
RoleArnSns=os.environ.get('ENV_SNS_TOPIC_ROLE_ARN')
bucket="fasstcondo-public-files"


# mongoURI = ssm.get_parameter( 
#     Name=PYTHON_DB_URL,
#     WithDecryption=True)['Parameter']['Value'] 

def handler(event,context):
    client = MClient()
    Property = client['Fasst']
    PropFromBridge = Property['PropFromBridge']
    # for record in event['Records']:
    
    try:
        data = event["data"]
        address=data['address']
        propertyDetails =PropFromBridge.find_one({'address':address})
        textractData(PropFromBridge,str(propertyDetails['_id']),propertyDetails)
    
    except:
        error= traceback.format_exc()
        print(error)
        
        




def MClient():
        client = MongoClient('mongodb://mongdbuser:%266XR%24a%25jS2W5z%5E%40%24@***********:27017/admin')
        # client = MongoClient("mongodb://localhost:27017")
        return client

def textractData(PropFromBridge, _id,propertyDetails):
    try:
        PropFromBridge.update_one({'_id': ObjectId(_id)},
                             {'$set': {'status.judgementFileTextract.status': 1}})
      

        # bucket = S3Location.split('/')[0]
        # file = S3Location.split('/')[1:] 
        # fileName = '/'.join(file)

        # response = textract.analyze_expense(
        #     Document = {'S3Object': {'Bucket': bucket,'Name': fileName }})

        # response = textract.start_expense_analysis(
        #     DocumentLocation={
        #         'S3Object': {
        #             'Bucket': bucket,
        #             'Name': f"judgment-{propertyDetails['caseDetails']['Case #:']}.pdf"
        #         }
        #     },
        #     NotificationChannel={
        #         'SNSTopicArn': SNSTopicArn, 
        #         'RoleArn': RoleArnSns   
        #     } 
        # )
        response = textract.start_document_text_detection(
            DocumentLocation={
                'S3Object': {
                    'Bucket': bucket,
                    'Name': propertyDetails['sirsReportKey']
                }
            },
            NotificationChannel={
                'SNSTopicArn': SNSTopicArn, 
                'RoleArn': RoleArnSns   
            } 
        )
        print(response)
        job_id = response['JobId']  
        # WorkItems.update_one({ '_id': ObjectId(_id) },{'$set':{'status.invoiceFileTextract.status' : 2,'blocks':response,"status.extractInvoiceData.status" : 0}})
        PropFromBridge.update_one({'_id': ObjectId(_id)}, {
                             '$set': {'status.judgementFileTextract.status': 2, "jobId": job_id,'status.getOcrResultsViaSns.status': 0}})
    except BaseException:
        traceStr = ''.join(traceback.format_exc())
        print(traceStr)
        PropFromBridge.update_one({'_id': ObjectId(_id)},
                             {'$set': {'status.judgementFileTextract.status': 4,
                                       'status.judgementFileTextract.error': traceStr}})
        return False
    
    
    
    


        