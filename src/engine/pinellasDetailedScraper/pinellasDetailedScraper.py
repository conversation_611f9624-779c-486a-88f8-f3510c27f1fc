from selenium import webdriver
from tempfile import mkdtemp
from selenium.webdriver.common.by import By

import traceback

import time
from io import StringIO

import json
from pymongo import MongoClient
from bson.objectid import ObjectId
from urllib.parse import urlparse, parse_qs


# from helpers import *


from bs4 import BeautifulSoup
import boto3
import pandas as pd
sqs_client = boto3.client('sqs')
queue_name = 'crawlAirDNA'
attributes = {
    # 'VisibilityTimeout': '300',
    # 'MessageRetentionPeriod': '86400'
}
cacheDB=None
# import sys
# sys.path.append('..')
def MClient():
    if cacheDB!=None:
        return cacheDB
    client = MongoClient("mongodb://mongdbuser:%266XR%24a%25jS2W5z%5E%40%24@***********:27017/admin")
    # client = MongoClient("mongodb://localhost:27017")
    return client


def handler(event,context):
    client = MClient()
    Property = client['Fasst']
    # Zillow = Property['Zillow']
    PropFromBridge = Property['PropFromBridge']
    PropertyAppraisal = Property['PropertyAppraisal']
    
    HabuDb=client['Property']
    Suffixes=HabuDb['Suffixes']
    AdvancedSearch=HabuDb['AdvancedSearch']
    # property=json.loads(event['Records'][0]['body'])
    # propertyId=property['propFromBridgeId']

    options = webdriver.ChromeOptions()
    service = webdriver.ChromeService("/opt/chromedriver")

    options.binary_location = '/opt/chrome/chrome'
    options.add_argument("--headless=new")
    options.add_argument('--no-sandbox')
    options.add_argument("--disable-gpu")
    options.add_argument("--window-size=1280x1696")
    options.add_argument("--single-process")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-dev-tools")
    options.add_argument("--no-zygote")
    options.add_argument(f"--user-data-dir={mkdtemp()}")
    options.add_argument(f"--data-path={mkdtemp()}")
    options.add_argument(f"--disk-cache-dir={mkdtemp()}")
    options.add_argument("--remote-debugging-port=9222")

    driver = webdriver.Chrome(options=options, service=service)
   
    # driver.get('https://www.google.nl/')

    # Hillsborough county start
    totalPinellascounty = 0
    # print(propertyId,"from shortlist")
    # for prop in Zillow.find({"detailUrl": "https://www.zillow.com/homedetails/9620-N-Oklawaha-Ave-Tampa-FL-33617/45074956_zpid/"}, {'addressStreet': 1, 'address': 1, '_id': 1}):
        # for prop in  PropFromBridge.find({"status.crawlAppraisal.status" : 0,"county" : "Hillsborough County, FL"},{'addressStreet' :1,'address':1,'_id' : 1}):
        # process all pending records {0}
    try:
        response = sqs_client.get_queue_url(QueueName=queue_name)
        queue_url = response['QueueUrl']
        print(f"Queue '{queue_name}' already exists with URL: {queue_url}")
    except sqs_client.exceptions.QueueDoesNotExist:
        print("ok its here")
        response = sqs_client.create_queue(QueueName=queue_name, Attributes=attributes)
        queue_url = response['QueueUrl']
        print(f"Created queue '{queue_name}' with URL: {queue_url}")
    for record in event['Records']:
        try:
            property=json.loads(record['body'])
        except:
            property=record['body']
        
        propertyId=property['propFromBridgeId']
        print(record,"record")
        print(propertyId,"idProp")
        prop=PropFromBridge.find_one({"_id":ObjectId(propertyId),
                                      "status.crawlDetailedAppraisal.status" : 0,
                                      }
                                     )
        # {"_id":ObjectId('64646b369391447a12b61708'),"status.crawlAppraisal.status" : 0,"County" : "Hillsborough County, FL"}, {'parcelNumber': 1, '_id': 1}
        # )


        PropFromBridge.update_one({"_id":prop['_id']}, {
                                "$set": {"status.crawlDetailedAppraisal.status": 1}})  # set status to running{1}
        print(prop)
        try:
            print("its working")
            # driver.get(f"https://www.pcpao.gov/quick-search?qu=1&input={prop['parcelNumber']}&search_option=parcel_number");
            
            driver.get("https://www.pcpao.gov/quick-search?qu=1")
            # print(driver.current_url)
            search_input=driver.find_element(by=By.XPATH,
                value='//*[@id="txtKeyWord"]')
            # print(prop['address'])
            search_input.click()
            
            search_input.send_keys(prop['address'])
            print("here")
            time.sleep(3)
        
            element = driver.find_element(by=By.XPATH,
                value='//*[@id="quickSearch"]/tbody/tr[1]/td[2]/a').click()
            # time.sleep(10)
            driver.switch_to.window(driver.window_handles[1])
            element = driver.find_element(by=By.XPATH,
                value='//*[@id="structural_1"]')
            driver.execute_script("arguments[0].style.display = 'block';", element)

            # appraiserUrl = driver.current_url
            time.sleep(5)
            html=driver.page_source
            
            soup = BeautifulSoup(html,'html.parser')
            # print(soup.text)
            # table = soup.find_all('table')
            parsedUrl = urlparse(driver.current_url)
            queryParams = parse_qs(parsedUrl.query)
            pinellasPropertyId = queryParams.get('s')[0] if 's' in queryParams else None

        
            
            propertyDetails={}
            propertyDetails['propFromBridgeId'] = prop['_id']
            propertyDetails['County'] = prop['County']
            propertyDetails['url']=driver.current_url
            propertyDetails['pinellasPropertyId']=pinellasPropertyId
            propertyDetails['sketch']=f"https://www.pcpao.gov/dal/blob/getBuilding/{pinellasPropertyId}/1"

            
            
            
            
            
        
            table = soup.find_all('table')
                    
            # SUB AREA INFO
            df = pd.read_html(str(table))[10]
            d = df.to_dict(orient='records')
            j = json.dumps(d)
            html_json=json.loads(j)
            propertyDetails['SubAreaInformation']=html_json
            
            # BUILDING STRUCTURAL ELEMENTS
            buildingStructuralElements = pd.read_html(str(table))[9]
            buildingStructuralElementsD = buildingStructuralElements.to_dict(orient='records')
            buildingStructuralElementsJ = json.dumps(buildingStructuralElementsD)
            buildingStructuralElementsJson=json.loads(buildingStructuralElementsJ)
            propertyDetails['buildingStructuralElements']=buildingStructuralElementsJson
            


            # SALES HISTORY
            salesTable = soup.find(id='tblSalesHistory')
            salesJsonList = pd.read_html(str(salesTable), flavor='bs4')[0].to_json(orient='records')
            propertyDetails['salesHistory']=json.loads(salesJsonList)
            
            # LAND INFORMATION
            landInformation = soup.find(id='tblLandInformation')
            landInformationJsonList = pd.read_html(str(landInformation), flavor='bs4')[0].to_json(orient='records')
            propertyDetails['landInformation']=json.loads(landInformationJsonList)
                
            
            # # PERMIT DATA
            # permitInformation = soup.find(id='tblPermit')
            # permitInformationJsonList = pd.read_html(str(permitInformation), flavor='bs4')[0].to_json(orient='records')
            # print(permitInformationJsonList)
            # propertyDetails['permitInformation']=json.loads(permitInformationJsonList)
            

            
            OwnerName = soup.find(id='first_second_owner')
            if OwnerName:
                propertyDetails['owner'] = OwnerName.get_text()
            else:
                propertyDetails['owner'] =""
                print("Element with the specified ID not found.")
            siteAddress = soup.find(id='site_address')
            if siteAddress:
                propertyDetails['siteAddress'] = siteAddress.get_text()
            else:
                propertyDetails['siteAddress'] = ""
                print("Element with the specified ID not found.")
            mailingAddress = soup.find(id='mailling_add')
            if mailingAddress:
                propertyDetails['mailingAddress'] = siteAddress.get_text()
                print(propertyDetails['mailingAddress'])
            else:
                propertyDetails['mailingAddress'] = ""
                print("Element with the specified ID not found.")
                
            streetAddress=prop['address']
            addressParts=streetAddress.split(' ')
            # suffixDetails=Suffixes.find_one({'Suffix':addressParts[-1]})
            # sinkholeSearch=True
            
            
            # finding Subsidence for pinellas county
            while True:
                addressPartsLength=len(addressParts)
                
                suffixDetails=Suffixes.find_one({'Suffix':addressParts[addressPartsLength-1]})
                # print(addressParts)
                if addressPartsLength == -1:
                    break
                if suffixDetails is not None:
                    addressParts.pop()
                    addressParts.append(suffixDetails['Abbreviation'])
                    streetAddress=' '.join(addressParts)
                    
                    sinkholeDetails=AdvancedSearch.find_one({'Property Address':{'$regex':streetAddress}})
                    if sinkholeDetails is not None:
                        propertyDetails['Subsidence']=sinkholeDetails['Subsidence']
                        break
                    if sinkholeDetails is None:
                        addressParts.pop()
                if suffixDetails is None:
                    addressParts.pop()
                
            
            
            
            # if sinkholeDetails is not None:
            #     propertyDetails['Subsidence']=sinkholeDetails['Subsidence']

            # inserting into Property database

          

         
            
         
                
            
            PropertyAppraisal.insert_one(propertyDetails)
            totalPinellascounty += 1
            PropFromBridge.update_one({"_id": prop['_id']}, {
                                    "$set": {"status.crawlDetailedAppraisal.status": 2,
                                            }})

        except:
            error = traceback.format_exc()
            print(error)
            PropFromBridge.update_one({"_id": ObjectId(propertyId)}, {"$set": {
                                    "status.crawlDetailedAppraisal.status": 5, "status.crawlDetailedAppraisal.error": error}})
           
            


    # Hillsborough county end
    print('Pinellas county appraisal:', totalPinellascounty)
    slackMessage = ' Pinellas county appraisal ' + str(totalPinellascounty)
    # send_message_to_slack(slackMessage)
    driver.quit()
    


# handler({
#   "Records": [
#     {
#        "body": "{\"propFromBridgeId\": \"66070c3d8bd851631387f29e\"}"
      
      
#     }
#   ]
# },2)