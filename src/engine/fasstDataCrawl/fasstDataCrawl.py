import os
import sys
from datetime import datetime
from pymongo import MongoClient
import json
import urllib.request
import boto3

sys.path.append('..')
sqs_client = boto3.client('sqs')


pythonDBUrl="mongodb://mongdbuser:%266XR%24a%25jS2W5z%5E%40%24@3.83.138.34:27017/admin"

# Get the SQS queue URLs from environment variables
PINELLAS_PROPERTY_APPRAISER_QUEUE_URL = os.environ.get('PINELLAS_PROPERTY_APPRAISER_QUEUE_URL')
PINELLAS_PERMIT_SCRAPER_QUEUE_URL = os.environ.get('PINELLAS_PERMIT_SCRAPER_QUEUE_URL')
PINELLAS_DETAILED_SCRAPPER_URL = os.environ.get('PINELLAS_DETAILED_SCRAPPER_URL')


def m_client(url):
        client = MongoClient(url)
        
        return client


def handler(event, context):
    
    
    pythonClient=m_client(pythonDBUrl)
    Property = pythonClient['Fasst']
    PropFromBridge = Property['PropFromBridge']
    

    
    
    # condoRequestUrl =   'https://api.bridgedataoutput.com/api/v2/OData/stellar/Property?access_token=be69e8d2e8e68d3b377d8c63fdf590af&$top=200&$filter=StandardStatus%20eq%20%27Active%27%20and%20CountyOrParish%20eq%20%27Pinellas%27%20and%20PropertySubType%20eq%20%27Condominium%27&$orderby=OnMarketTimestamp%20desc'
    condoRequestUrl =   'https://api.bridgedataoutput.com/api/v2/OData/stellar/Property?access_token=be69e8d2e8e68d3b377d8c63fdf590af&$top=200&$filter=StandardStatus%20eq%20%27Active%27%20and%20CountyOrParish%20eq%20%27Pinellas%27%20and%20PropertySubType%20eq%20%27Condominium%27%20and%20PropertyType%20eq%20%27Residential%27&$orderby=OnMarketTimestamp%20desc'
    # def main():
    request_url = urllib.request.urlopen(condoRequestUrl)
    propFromBridge = (request_url.read())
    propFromBridge = propFromBridge.decode()

    pos1 = propFromBridge.find('value')
    pos2 = propFromBridge.rindex('}')
    propFromBridge = propFromBridge[pos1:pos2]
    propFromBridge = '{"' + propFromBridge +'}'

    propFromBridge = json.loads(propFromBridge)
    new_ids = []  # Track inserted IDs
    for prop in propFromBridge["value"]:
        current_property=PropFromBridge.find_one({"address":prop['UnparsedAddress']})
        if current_property:
            try:
                if current_property['rawData']['StandardStatus']=='Pending' and prop['StandardStatus']=='Active':

                    PropFromBridge.delete_one({'_id':current_property['_id']})
                else:
                    PropFromBridge.update_one({'_id':current_property['_id']},{'$set':{'latestUpdateTime':datetime.now()}})
                    continue
            except Exception as e:
                print(e)
        print("data not exist")  
        dataToBeInserted = {}
        dataToBeInserted['Source']="Stellar New"
        dataToBeInserted["LivingAreaUnits"] = prop["LivingAreaUnits"]
        dataToBeInserted['beds'] = prop["BedroomsTotal"]
        dataToBeInserted["bathroom"] = prop["BathroomsTotalDecimal"]
        dataToBeInserted['propertyStatus'] =prop["MlsStatus"]
        
        dataToBeInserted['onMarketTimestamp'] = prop.get('OnMarketTimestamp')
        
        dataToBeInserted['ZoningCode'] = prop["Zoning"]
        dataToBeInserted['County'] = prop['CountyOrParish']
        dataToBeInserted['description']=prop['PublicRemarks']
        dataToBeInserted['taxDescription'] =  prop['TaxLegalDescription']
        dataToBeInserted["heatedSpace"] = prop["LivingArea"]
        dataToBeInserted["homeType"] = prop["PropertySubType"]
        dataToBeInserted["lotSize"] = prop["LotSizeSquareFeet"]
        dataToBeInserted["askingPrice"] = prop["ListPrice"]
        dataToBeInserted['parcelNumber'] = prop["ParcelNumber"]
        dataToBeInserted['year'] = prop['YearBuilt']
        dataToBeInserted['createdAt']=datetime.now()
        latLong = {}
        latLong['latitude'] = prop['Latitude']
        latLong['longitude'] = prop['Longitude']
        dataToBeInserted['latLong'] = latLong
        dataToBeInserted['Waterfront']=prop['WaterfrontYN']
        dataToBeInserted['Status']=prop['OccupantType']
        dataToBeInserted['ListAgentNamePrefix']=prop['ListAgentNamePrefix']
        dataToBeInserted['ListAgentEmail']=prop['ListAgentEmail']
        dataToBeInserted['ListAgentPreferredPhone']=prop['ListAgentPreferredPhone']
        dataToBeInserted['ListOfficeName']=prop['ListOfficeName']
        
        
        
        
        
        
        # Set 'regularAssessmentAmount' from STELLAR_CondoFees if available, else calculate from AssociationFee and frequency.
        if prop.get('STELLAR_CondoFees') is not None and prop.get('STELLAR_CondoFees') != 0:
            dataToBeInserted['regularAssessmentAmount'] = prop['STELLAR_CondoFees']
            print("Using STELLAR_CondoFees:", prop['STELLAR_CondoFees'])
        else:
            assoc_fee = prop.get('AssociationFee')
            freq = (prop.get('AssociationFeeFrequency') or '').lower()

            if assoc_fee is not None:
                if freq == 'annual':
                    calculated_fee = assoc_fee / 12
                elif freq == 'quarterly':
                    calculated_fee = assoc_fee / 4
                else:  # monthly or unknown
                    calculated_fee = assoc_fee

                print(f"Using AssociationFee ({assoc_fee}) with frequency '{freq}', calculated regularAssessmentAmount: {calculated_fee}")
                dataToBeInserted['regularAssessmentAmount'] = calculated_fee
            else:
                print("AssociationFee not available.")
                dataToBeInserted['regularAssessmentAmount'] = None


        if prop.get('STELLAR_CondoFeesTerm') is not None:
             dataToBeInserted['frequencyOfRegularAssessment'] = prop['STELLAR_CondoFeesTerm']
             print("Using STELLAR_CondoFeesTerm:", prop['STELLAR_CondoFeesTerm'])
        else:
            print("Using AssociationFeeFrequency:", prop.get('AssociationFeeFrequency'))
            dataToBeInserted['frequencyOfRegularAssessment'] = prop.get('AssociationFeeFrequency')
            
        if "Media" in prop and prop["Media"] and len(prop["Media"]) > 0:
            try:
                 dataToBeInserted["mediaUrl"] = prop["Media"][0]["MediaURL"]
                 print("Media URL:", dataToBeInserted["mediaUrl"])
            except (KeyError, IndexError):
                dataToBeInserted["mediaUrl"] = None
                print("No valid media URL found") 
   

        try :
            dataToBeInserted['address'] = prop['UnparsedAddress']
            dataToBeInserted['addressStreet'] = prop['UnparsedAddress'].split(',')[0]
            dataToBeInserted['addressCity']=prop['City']
            dataToBeInserted['addressState'] =prop['StateOrProvince']
            dataToBeInserted['addressZipcode']=prop['PostalCode']

            
        except Exception as e :
            print(e)
        dataToBeInserted['status' ]= {'userRequest': {'status':0}, 'crawlPermits': {'status':0}, 'crawlEntireAppartmentPermits': {'status':0},'crawlDetailedAppraisal':{'status':0}}
        try:
            result = PropFromBridge.insert_one(dataToBeInserted)
            new_ids.append(str(result.inserted_id))

        except Exception as e:
            print(e)

    if new_ids:
        for _id in new_ids:
            message_for_pinellas_queue = {
              "propFromBridgeId": _id
            }

            # Send to both queues
            sqs_client.send_message(
                QueueUrl=PINELLAS_PROPERTY_APPRAISER_QUEUE_URL,
                MessageBody=json.dumps(message_for_pinellas_queue)
            )
            sqs_client.send_message(
                QueueUrl=PINELLAS_PERMIT_SCRAPER_QUEUE_URL,
                MessageBody=json.dumps(message_for_pinellas_queue)
            )
            
            sqs_client.send_message(
                QueueUrl=PINELLAS_DETAILED_SCRAPPER_URL,
                MessageBody=json.dumps(message_for_pinellas_queue)
            )

        return {
            "statusCode": 200,
            "body": f"{len(new_ids)} new records inserted and messages sent to both SQS queues."
        }
    else:
        return {
            "statusCode": 200,
            "body": "No new records inserted. Data is already up-to-date."
        }

           
# handler(1,2)