import os
import json
import re
import time
import random
import traceback
from tempfile import mkdtemp
from urllib.parse import urlparse, parse_qs

import boto3
from bson.objectid import ObjectId
from pymongo import MongoClient

# Selenium and related imports
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver import Keys
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException

from bs4 import BeautifulSoup
import pandas as pd

# Global cache for Mongo client
cacheDB = None

def get_mongo_client():
    global cacheDB
    if cacheDB is not None:
        return cacheDB
    # Use your MongoDB connection string
    cacheDB = MongoClient("mongodb://mongdbuser:%266XR%24a%25jS2W5z%5E%40%24@3.83.138.34:27017/admin")
    return cacheDB

def format_parcel_id(parcel_id):
    # This pattern assumes exactly 18 digits, grouped as: 2-2-2-5-3-4
    pattern = r"^(\d{2})(\d{2})(\d{2})(\d{5})(\d{3})(\d{4})$"
    match = re.match(pattern, parcel_id)
    if match:
        return "-".join(match.groups())
    else:
        # If the parcel ID doesn't match the expected 18-digit pattern,
        # handle the error or return the original
        return parcel_id

def scrape_permit_details(driver, parcel_id):
    """
    Scrape permit details for the provided parcel_id.
    Returns a list of permits (or an empty list if none found).
    """
    try:
        parcel_id = format_parcel_id(parcel_id)
        print(f"Processing parcel: {parcel_id}")
        driver.get("https://www.pcpao.gov/quick-search?qu=1")
        
        # Optionally click the parcel id radio button
        try:
            radio_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "#sub1 > div.col-md-8.searchPrintArea > div.col-search-propery-tab > p:nth-child(3) > label"))
            )
            radio_button.click()
            print("Parcel id radio button clicked")
        except Exception as e:
            print("Parcel id radio button not found or click failed:", e)
        
        # Wait for the search input field
        search_input = WebDriverWait(driver, 30).until(
            EC.visibility_of_element_located((By.CSS_SELECTOR, "#txtKeyWord"))
        )
        # search_input.send_keys(Keys.CONTROL + "a")
        # search_input.send_keys(Keys.DELETE)
        # for char in parcel_id:
        #     search_input.send_keys(char)
        #     time.sleep(random.uniform(0.08, 0.15))
        # Clear the field
        search_input.clear()

        # Send the whole parcel ID at once
        search_input.send_keys(parcel_id)
        
        # Wait for search results to load
        try:
            # rows = WebDriverWait(driver, 60).until(
            #     EC.presence_of_all_elements_located((By.CSS_SELECTOR, "#quickSearch tbody tr"))
            # )
            # print(f"DEBUG - Number of search result rows: {len(rows)}")
            time.sleep(10)
            element = driver.find_element(by=By.XPATH,
                value='//*[@id="quickSearch"]/tbody/tr[1]/td[2]/a').click()
            print("Clicked the first search result row")
        except TimeoutException:
            print("DEBUG - No search result rows found.")
            snippet = driver.page_source[:1000]
            print("Page source snippet:", snippet)
            return []
        
        # Click the first search result
        try:
            first_row = driver.find_element(By.CSS_SELECTOR, "#quickSearch tbody tr:first-child")
            driver.execute_script("arguments[0].click();", first_row)
            print("Clicked the first search result row")
        except Exception as e:
            print("First result row not found:", e)
            return []
        
        # Wait for a new tab and switch to it
        # WebDriverWait(driver, 30).until(lambda d: len(d.window_handles) == 2)
        # driver.switch_to.window(driver.window_handles[1])
        driver.close()  # Closes the current tab
        driver.switch_to.window(driver.window_handles[0])
        
        # Change pagination to show "All" entries (if available)
        try:
            pagination_select = WebDriverWait(driver, 30).until(
                EC.visibility_of_element_located((By.NAME, "tblPermit_length"))
            )
            select = Select(pagination_select)
            select.select_by_value("-1")
            print("Pagination set to All")
            time.sleep(3)
        except Exception as e:
            print("Failed to change pagination:", e)
        
        # Wait for the permit table to load
        try:
            permit_table = WebDriverWait(driver, 40).until(
                EC.presence_of_element_located((By.XPATH, '//*[@id="tblPermit"]'))
            )
        except TimeoutException:
            print("Using fallback table detection")
            permit_table = driver.find_element(By.XPATH, "//h2[contains(., 'Permit Data')]/following-sibling::table")
        
        # Parse the permit table using BeautifulSoup
        soup = BeautifulSoup(permit_table.get_attribute('outerHTML'), 'html.parser')
        permits = parse_permit_table(soup)
        return permits

    except Exception as e:
        driver.save_screenshot(f'error_{parcel_id}.png')
        print(f"Scraping failed: {str(e)}")
        return []

def parse_permit_table(soup):
    """
    Parse the HTML table containing permit data and extract details.
    """
    permits = []
    try:
        tbody = soup.find('tbody')
        rows = tbody.find_all('tr') if tbody else soup.find_all('tr')
        if not rows:
            print("No rows found in the permit table.")
            return []
        for row in rows:
            cols = row.find_all('td')
            if len(cols) < 4:
                continue
            permit_num = cols[0].get('data-order', '').strip()
            if not permit_num or permit_num == "*****":
                a_tag = cols[0].find('a')
                if a_tag:
                    permit_num = a_tag.get_text(strip=True)
            permit = {
                "permitNumber": permit_num,
                "description": cols[1].get_text(strip=True),
                "issueDate": cols[2].get_text(strip=True),
                "estimatedValue": cols[3].get_text(strip=True)
            }
            permits.append(permit)
    except Exception as e:
        print(f"Table parsing error: {str(e)}")
    return permits

def update_database(prop_id, permits):
    """
    Update the MongoDB document with the scraped permit details.
    """
    client = get_mongo_client()
    db = client['Fasst']
    db['PropFromBridge'].update_one(
        {"_id": ObjectId(prop_id)},
        {"$set": {"permitDetails": permits}}
    )

def handler(event, context):
    """
    Lambda handler function triggered by SQS events.
    Processes each record, scrapes permit details, and updates MongoDB.
    """
    # Initialize Selenium WebDriver
    try:
        options = webdriver.ChromeOptions()
        service = webdriver.ChromeService("/opt/chromedriver")
        options.binary_location = '/opt/chrome/chrome'
        options.add_argument("--headless=new")
        options.add_argument('--no-sandbox')
        options.add_argument("--disable-gpu")
        options.add_argument("--window-size=1280x1696")
        options.add_argument("--single-process")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-dev-tools")
        options.add_argument("--no-zygote")
        options.add_argument(f"--user-data-dir={mkdtemp()}")
        options.add_argument(f"--data-path={mkdtemp()}")
        options.add_argument(f"--disk-cache-dir={mkdtemp()}")
        options.add_argument("--remote-debugging-port=9222")
        driver = webdriver.Chrome(options=options, service=service)
    except Exception as e:
        print("Failed to initialize WebDriver:", e)
        raise

    total_processed = 0
    try:
        for record in event.get("Records", []):
            try:
                message_body = json.loads(record["body"])
                property_id = message_body.get("propFromBridgeId")
                print(f"Pinellas Permit: Processing property_id: {property_id}")
                
                # Retrieve property document from MongoDB
                client = get_mongo_client()
                db = client["Fasst"]
                prop_collection = db["PropFromBridge"]
                prop = prop_collection.find_one({
                    "_id": ObjectId(property_id),
                    "status.crawlPermits.status": 0,
                })
                if not prop:
                    print(f"Pinellas Permit: No matching property found or already processed for ID: {property_id}")
                    continue

                # Mark property as in-progress (status=1)
                prop_collection.update_one(
                    {"_id": ObjectId(property_id)},
                    {"$set": {"status.crawlPermits.status": 1}}
                )

                # Use parcel number for scraping
                parcel_id = prop.get("parcelNumber")
                if not parcel_id:
                    print(f"Pinellas Permit: No parcelNumber or address found for property_id: {property_id}")
                    continue

                # Scrape permit details
                permits = scrape_permit_details(driver, parcel_id)
                if permits:
                    update_database(property_id, permits)
                    # Mark as successful (status=2)
                    prop_collection.update_one(
                        {"_id": ObjectId(property_id)},
                        {"$set": {"status.crawlPermits.status": 2, "status.crawlPermits.error": None}}
                    )
                    print(f"Pinellas Permit: Successfully processed property_id: {property_id}")
                else:
                    print(f"Pinellas Permit: No permits found for property_id: {property_id}")
                    # Optionally update status to reflect no data found (status=3)
                    prop_collection.update_one(
                        {"_id": ObjectId(property_id)},
                        {"$set": {"status.crawlPermits.status": 3, "status.crawlPermits.error": "No permits found"}}
                    )
                total_processed += 1

            except Exception as e:
                error_msg = traceback.format_exc()
                print(f"Pinellas Permit: Error processing record: {error_msg}")
                if "property_id" in locals() and property_id:
                    client = get_mongo_client()
                    db = client["Fasst"]
                    prop_collection = db["PropFromBridge"]
                    prop_collection.update_one(
                        {"_id": ObjectId(property_id)},
                        {"$set": {"status.crawlPermits.status": 5, "status.crawlPermits.error": str(error_msg)}}
                    )
        print(f"Pinellas Permit: Total properties processed: {total_processed}")
        return {
            "statusCode": 200,
            "body": f"Processed {total_processed} property records."
        }
    finally:
        driver.quit()


# handler({
#   "Records": [
#     {
#        "body": "{\"propFromBridgeId\": \"67aa219c93a04d07cb3b8c47\"}"
#     }
#   ]
# },2)