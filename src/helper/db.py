from pymongo import MongoClient
import os
import boto3


def get_mongo_uri():
    try:
        # Directly fetch the URI from the environment variables
        mongo_uri = os.environ.get("MONGO_CONNECTION_URI")
        
        if not mongo_uri:
            raise Exception("Mongo connection URI not found in environment variables")

        return mongo_uri

    except Exception as e:
        print(f"Error fetching Mongo URI: {str(e)}")
        raise  # Re-raise the error to be handled by the calling function

# Cached database connection
cache_db = None

def connect_db():
    global cache_db
    try:
        if cache_db is not None:
            return cache_db

        mongo_uri = get_mongo_uri()
        client = MongoClient(mongo_uri)

        db_name = os.environ.get("MONGO_DB_NAME", "Fasst")
        db = client[db_name]

        cache_db = db
        return db

    except Exception as e:
        print(f"Error connecting to MongoDB: {str(e)}")
        raise 

def get_email_sender():
    """Get email sender from environment variables."""
    email_sender = os.environ.get("EMAIL_SENDER")
    if not email_sender:
        raise Exception("Email sender not found in environment variables")
    return email_sender

def get_email_recipients():
    """Get email recipients from environment variables."""
    email_recipients = os.environ.get("EMAIL_RECIPIENTS")
    if not email_recipients:
        raise Exception("Email recipients not found in environment variables")
    return email_recipients.split(",")  # Split if multiple recipients are provided
