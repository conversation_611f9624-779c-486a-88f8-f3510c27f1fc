import json
import boto3
import logging
import os
import sys

sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)
from helper.db import connect_db
# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

def handler(event, context):
    cognito = boto3.client('cognito-idp')
    common_headers = {
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "OPTIONS,POST",
    }
    
    db = connect_db()
    collection = db["Users"]
    
    try:
        
        logger.info("Received Event: %s", str(event))
        client_id = os.environ['USER_POOL_CLIENT_ID']
        
        
        
        if 'body' in event:
            body = json.loads(event['body'])
        elif 'queryStringParameters' in event and event['queryStringParameters']:
            body = event['queryStringParameters']
        else:
            raise ValueError("Unable to find body in event")

        
        logger.info("Parsed Body: %s", str(body))

        email = body.get('email')
        confirmation_code = body.get('otp')
        password=body.get('password')

        if not all([email, confirmation_code]):
            raise ValueError("Missing required fields in body")

        
        response = cognito.confirm_sign_up(
            ClientId=client_id, 
            Username=email,
            ConfirmationCode=confirmation_code
        )
        print(response)
        response = cognito.initiate_auth(
            ClientId=client_id,
            AuthFlow='USER_PASSWORD_AUTH',
            AuthParameters={
                'USERNAME': email,
                'PASSWORD': password
            }
        )
        id_token = response['AuthenticationResult'].pop('IdToken')
            #  In Backgroud, we can add user to the database
        collection.insert_one({'email':email})
        
        logger.info(f"User {email} confirmed successfully.")
        
        return {
            'statusCode': 200,
            'body': json.dumps({'message':'User account confirmed successfully.','token':id_token}),
             "headers": common_headers,
            
        }

    except cognito.exceptions.CodeMismatchException:
        return {
            'statusCode': 400,
            'body': json.dumps({'message':'Invalid confirmation code.'}),
             "headers": common_headers,
            
        }
    except cognito.exceptions.ExpiredCodeException:
        return {
            'statusCode': 400,
            'body': json.dumps({'message':'Confirmation code has expired.'}),
             "headers": common_headers,
            
        }
    except cognito.exceptions.UserNotFoundException:
        return {
            'statusCode': 400,
            'body': json.dumps({'message':'User not found.'}),
             "headers": common_headers,
            
        }
    except ValueError as ve:
        logger.error(f"ValueError: {ve}")
        return {
            'statusCode': 400,
            'body': json.dumps({'message':f"Invalid input: {str(ve)}"}),
             "headers": common_headers,
            
        }
    except Exception as e:
        logger.error(f"Error confirming user: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({'message':f"Error confirming user: {str(e)}"}),
             "headers": common_headers,
            
        }
