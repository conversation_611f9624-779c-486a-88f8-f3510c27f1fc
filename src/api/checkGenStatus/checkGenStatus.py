import json
from pymongo import MongoClient
import sys
import os

# Add the src directory to the Python path
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from helper.db import connect_db


def lambda_handler(event, context):
    # MongoDB connection
    db = connect_db()
    collection_name = "PropFromBridge"
    collection = db[collection_name]

    # Get the type from the API request body
    data = json.loads(event["body"])
    type_ = data.get("type")
    address = data.get("address")

    # Check if the type is valid
    if type_ not in ["SIRSPdfGenAi", "milestonePdfGenAi"]:
        return {
            "statusCode": 400,
            "body": json.dumps({"error": "Invalid type provided."}),
            "headers": {
                "Access-Control-Allow-Headers": "Content-Type",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS,POST",
            },
        }

    # Get the status based on the type
    document = collection.find_one({"address": address})

    if document:
        if type_ == "SIRSPdfGenAi":
            if document["status"]["SIRSPdfGenAi"]["status"] == 2:
                return {
                    "statusCode": 200,
                    "body": json.dumps(
                        {
                            "status": document["status"]["SIRSPdfGenAi"]["status"],
                            "sirsAiResponse": document["sirsAiResponse"],
                        }
                    ),
                    "headers": {
                        "Access-Control-Allow-Headers": "Content-Type",
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS,POST",
                    },
                }
            return {
                "statusCode": 200,
                "body": json.dumps(
                    {"status": document["status"]["SIRSPdfGenAi"]["status"]}
                ),
                "headers": {
                    "Access-Control-Allow-Headers": "Content-Type",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS,POST",
                },
            }
        elif type_ == "milestonePdfGenAi":
            
            if document["status"]["milestonePdfGenAi"]["status"] == 2:
                return {
                    "statusCode": 200,
                    "body": json.dumps(
                        {
                            "status": document["status"]["milestonePdfGenAi"]["status"],
                            "milestoneAiResponse": document["milestoneAiResponse"],
                        }
                    ),
                    "headers": {
                        "Access-Control-Allow-Headers": "Content-Type",
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS,POST",
                    },
                }
            return {
                "statusCode": 200,
                "body": json.dumps(
                    {"status": document["status"]["milestonePdfGenAi"]["status"]}
                ),
                "headers": {
                    "Access-Control-Allow-Headers": "Content-Type",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS,POST",
                },
            }
        else:
            return {
                "statusCode": 200,
                "body": json.dumps({"status": 3}),
                "headers": {
                    "Access-Control-Allow-Headers": "Content-Type",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS,POST",
                },
            }
    else:
        result = {"status": "not found"}

    return {"statusCode": 200, "body": json.dumps(result)}
