# Use the official AWS Lambda Python base image
FROM public.ecr.aws/lambda/python:3.9

# Set a working directory inside the container
WORKDIR /var/task

# Copy the Lambda function code into the container
COPY . /var/task

# Install any additional dependencies required by your function
# For example, if you need other packages alongside NumPy:
RUN pip install  pymongo google.generativeai pymupdf4llm

# Set the handler for your Lambda function
# Replace `lambda_function` with the actual name of your handler
<PERSON><PERSON> ["milestonePdfGenAi.lambda_handler"]