# Use the official AWS Lambda Python base image
FROM public.ecr.aws/lambda/python:3.10

# Install build tools required for compiling packages
RUN yum update -y && \
    yum install -y gcc gcc-c++ make && \
    yum clean all

# Set a working directory inside the container
WORKDIR /var/task

# Copy the Lambda function code into the container
COPY . /var/task

# Install any additional dependencies required by your function
# Use pymupdf instead of pymupdf4llm to avoid C++20 compilation issues
RUN pip install --upgrade pip && \
    pip install pymongo google.generativeai pymupdf

# Set the handler for your Lambda function
# Replace `lambda_function` with the actual name of your handler
<PERSON><PERSON> ["milestonePdfGenAi.lambda_handler"]