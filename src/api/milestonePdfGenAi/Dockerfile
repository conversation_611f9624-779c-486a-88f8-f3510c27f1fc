# Use the official AWS Lambda Python base image
FROM public.ecr.aws/lambda/python:3.12

# Install build tools required for compiling packages
RUN microdnf update -y && \
    microdnf install -y gcc gcc-c++ make && \
    microdnf clean all

# Set a working directory inside the container
WORKDIR /var/task

# Copy the Lambda function code into the container
COPY . /var/task

# Install any additional dependencies required by your function
RUN pip install --upgrade pip && \
    pip install pymongo google.generativeai pymupdf4llm

# Set the handler for your Lambda function
# Replace `lambda_function` with the actual name of your handler
<PERSON><PERSON> ["milestonePdfGenAi.lambda_handler"]