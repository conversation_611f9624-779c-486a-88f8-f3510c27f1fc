import json
import hashlib
import os
import base64
from pymongo import MongoClient
import logging
import jwt
from datetime import datetime, timedelta
import traceback

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize MongoDB client
client = MongoClient(os.environ['MONGO_CONNECTION_URI'])
db = client.Fasst
collection = db.admin

SECRET_KEY =os.environ.get('JWT_SECRET_KEY')

def hash_password(password):
    salt = os.urandom(32)
    key = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt, 100000)
    return base64.b64encode(salt + key).decode('utf-8')
# Generate hash for "admin_password"
hashed_password = hash_password("admin_password")


def verify_password(stored_password, provided_password):
    try:
        stored_password = base64.b64decode(stored_password)
        salt = stored_password[:32]
        stored_key = stored_password[32:]
        new_key = hashlib.pbkdf2_hmac('sha256', provided_password.encode('utf-8'), salt, 100000)
        return stored_key == new_key
    except Exception as e:
        logger.error(f"Error verifying password: {str(e)}")
        return False

def create_token(user_id):
    """
    Create a JWT token
    """
    payload = {
        'user_id': user_id,
        'exp': datetime.now() + timedelta(days=1)  # Token expires in 1 day
    }
    token = jwt.encode(payload, str(SECRET_KEY), algorithm='HS256')
    return token


def handler(event, context):
        common_headers = {
            "Access-Control-Allow-Headers": "Content-Type",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "OPTIONS,POST",
        }
    
        if 'body' not in event:
            return {
                'statusCode': 400,
                'headers': common_headers,
                'body': json.dumps({'message': 'Request body is missing'})
            }
    
        try:
            body = json.loads(event['body'])
        except json.JSONDecodeError:
            return {
                'statusCode': 400,
                'headers': common_headers,
                'body': json.dumps({'message': 'Invalid JSON format'})
            }
    
        username = body.get('username')
        password = body.get('password')
        
        if not username or not password:
            return {
                'statusCode': 400,
                'headers': common_headers,
                'body': json.dumps({'message': 'Username and password are required'})
            }
    
        admin = collection.find_one({"username": username})
        
        if admin:
            try:
                stored_password = admin['password']
                if verify_password(stored_password, password):
                    token=create_token(username)
                    return {
                        'statusCode': 200,
                        'headers': common_headers,
                        'body': json.dumps({'message': 'Login successful','token':token,'username':username})
                    }
                else:
                    return {
                        'statusCode': 401,
                        'headers': common_headers,
                        'body': json.dumps({'message': 'Invalid password'})
                    }
            except Exception as e:
                logger.error(f"Error during login process: {str(e)}")
               
                return {
                    'statusCode': 500,
                    'headers': common_headers,
                    'body': json.dumps({'message': 'Internal server error during login process'})
                }
        else:
            return {
                'statusCode': 404,
                'headers': common_headers,
                'body': json.dumps({'message': 'User not found'})
            }