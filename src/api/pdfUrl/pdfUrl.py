import json
import boto3
import os

# Initialize the S3 client
s3_client = boto3.client(
    's3',
    region_name='us-east-1',
    aws_access_key_id='********************',  # Set your access key
    aws_secret_access_key='Ixwgf1zF7xSElxjbqdHjTs6qyNNUy5A7i+JCsboi'  # Set your secret key
)
common_headers = {
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "OPTIONS,POST",
    }

def handler(event, context):
    try:
        # Get the address from the query parameters
        body=json.loads(event['body'])
        address = body['address']
        if not address:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'Address parameter is required'}),
                'headers': common_headers,
            }

        key = f"{address}.pdf"

        # Generate a presigned URL that expires in 1 hour (3600 seconds)
        url = s3_client.generate_presigned_url('get_object',
            Params={'Bucket': 'generate-report-fasst-python', 'Key': key,'ResponseContentType': 'application/pdf'},
            ExpiresIn=3600
        )
        
        print(f"Generated presigned URL: {url}")  # Debugging log
        return {
            'statusCode': 200,
            'body': json.dumps({'url': url}),
            'headers': common_headers,

        }
    except Exception as e:
        print(f"Error fetching image from S3: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({'error': 'Error fetching image from S3'}),
            'headers': common_headers,
            
        }
