# Use the official AWS Lambda Python base image
FROM public.ecr.aws/lambda/python:3.9

# Set a working directory inside the container
WORKDIR /var/task

# Copy the Lambda function code into the container
COPY . /var/task

# Install required packages for the SIRS PDF Lambda function
RUN pip install --no-cache-dir \
    pymongo \
    boto3 \
    pydantic \
    langchain \
    langchain-openai \
    langchain-google-genai \
    llama-parse \
    amazon-textract-response-parser \
    python-dotenv

# # The trp package might need to be installed from GitHub if not available on PyPI
# RUN pip install --no-cache-dir git+https://github.com/aws-samples/amazon-textract-response-parser.git#egg=trp

# Set the handler for the Lambda function
CMD ["sirsPdfGenAi.lambda_handler"]