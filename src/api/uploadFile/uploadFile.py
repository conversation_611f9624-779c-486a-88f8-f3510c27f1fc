import json
import boto3
import os
import random

s3_client = boto3.client('s3', region_name='us-east-1')
URL_EXPIRATION_SECONDS = 300  # 5 minutes

common_headers = {
    "Access-Control-Allow-Headers": "Content-Type",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "OPTIONS,POST",
}

# Allowed content types for different file extensions
ALLOWED_FILE_TYPES = {
    "pdf": "application/pdf",
    "csv": "text/csv",
    "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
}

def handler(event, context):
    try:
        # Parse request body
        body = json.loads(event.get("body", "{}"))

        # Extract file type from request, default to 'pdf' if missing
        file_name = body.get("fileName")  # Default to PDF
        content_type = body.get('extension')

        # Validate file type
        if content_type is None:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": f"Unsupported file type: {content_type}"}),
                'headers': common_headers,
            }

        # Generate random ID and filename
        random_id = random.randint(0, 9999999)
        file_name = f"{random_id}-{file_name}"

        # Generate pre-signed URL
        upload_url = s3_client.generate_presigned_url(
            ClientMethod='put_object',
            Params={
                'Bucket': "fasstcondo-public-files",
                'Key': file_name,
                'ContentType': content_type
            },
            ExpiresIn=URL_EXPIRATION_SECONDS,
            HttpMethod='PUT'
        )

        print("Generated URL:", upload_url)

        return {
            'statusCode': 200,
            'body': json.dumps({
                'uploadURL': upload_url,
                'Key': file_name
            }),
            'headers': common_headers,
        }

    except Exception as e:
        print(f"Error generating upload URL: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': 'Failed to generate upload URL',
                'details': str(e)
            }),
            'headers': common_headers,
        }
