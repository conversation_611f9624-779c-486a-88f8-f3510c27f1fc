import json
import boto3
from pymongo import MongoClient

def handler(event, context):
    db_name = 'Fasst'
    collection_name = 'PropFromBridge'

    client = MongoClient("mongodb://mongdbuser:%266XR%24a%25jS2W5z%5E%40%24@***********:27017/admin")
    db = client[db_name]
    collection = db[collection_name]

    common_headers = {
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "OPTIONS,POST",
    }

    try:
        print(event)

        if 'body' in event:
            body = json.loads(event['body'])

            if 'address' in body:
                search_text = body['address']

                # Case-insensitive partial match
                addresses = list(collection.find(
                    {"address": {"$regex": search_text, "$options": "i"}},
                    {"_id": 0, "address": 1}
                ))

                result = json.dumps(addresses)

                return {
                    'statusCode': 200,
                    'body': result,
                    'headers': common_headers,
                }
            else:
                return {
                    'statusCode': 400,
                    'body': json.dumps({'error': "Missing 'address' in request body"}),
                    'headers': common_headers,
                }

        else:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': "Missing 'body' in event"}),
                'headers': common_headers,
            }

    except Exception as e:
        print(f"Error fetching addresses: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({'error': str(e)}),
            'headers': common_headers,
        }