# Use the official AWS Lambda Python base image
FROM public.ecr.aws/lambda/python:3.9

# Set a working directory inside the container
WORKDIR /var/task

# Copy the Lambda function code into the container
COPY . /var/task

# Install necessary dependencies for the function
RUN pip install pymongo==4.6.1 requests==2.32.3 pillow==10.2.0 PyMuPDF==1.25.5 tqdm==4.67.1 Spire.Pdf==10.12.1 reportlab==4.4.0 boto3==1.34.27

# Set the handler for your Lambda function
# CMD ["generateReport.handler"]
CMD ["fasstReport.handler"]
