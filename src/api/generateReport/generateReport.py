import fitz
import json
import re
import logging
import boto3
import gc
import os
import time
from botocore.exceptions import ClientError, NoCredentialsError
from tqdm import tqdm
import pymongo
from pymongo.errors import ConnectionFailure
from datetime import datetime, timezone
from spire.pdf.common import *
from spire.pdf import *
import traceback
from PIL import Image, ImageFilter
import tempfile
import requests
from io import BytesIO
from reportlab.lib import colors
from reportlab.lib.colors import HexColor
from reportlab.lib.pagesizes import letter, landscape
from reportlab.platypus import (
    SimpleDocTemplate, Table, TableStyle, PageBreak, 
    Spacer, Paragraph, BaseDocTemplate, Frame, PageTemplate
)
from reportlab.lib.units import inch
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from bson.objectid import ObjectId

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration constants
MONGO_URI = "mongodb://mongdbuser:%266XR%24a%25jS2W5z%5E%40%24@3.83.138.34:27017/admin" 
DATABASE_NAME = "Fasst"
COLLECTION_NAME = "PropFromBridge"
S3_BUCKET_NAME = "generate-report-fasst-python"
TEMPLATE_KEY = "template_fasst.pdf"

# CORS headers
common_headers = {
    "Access-Control-Allow-Headers": "Content-Type",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "OPTIONS,POST",
}

# Category name mapping
CATEGORY_TITLE_MAP = {
    "electricalcomponentselements": "Electrical Components",
    "firesafetycomponentselements": "Fire Safety Components",
    "concretecomponentselements": "Concrete Components",
    "roofingcomponentselements": "Roofing Components",
    "plumbingcomponentselements": "Plumbing Components",
    "windowcomponentselements": "Window Components",
    "doorcomponentselements": "Door Components",
    "paintingcomponentselements": "Painting Components",
    "buildingservicecomponentselements": "Building Service Components",
    "propertysitecomponentselements": "Property Site Components",
    "exteriorbuildingcomponentselements": "Exterior Building Components",
    "buildingservice": "Building Service",
    "exteriorbuilding": "Exterior Building",
    "propertysite": "Property Site",
    "firesafety": "Fire Safety",
    "fireproofingandfireprotection": "Fireproofing and Fire Protection",
    "waterproofingandexteriorpainting": "Waterproofing and Exterior Painting",
    "windowsandexteriordoors": "Windows and Exterior Doors",
    "siteimprovements": "Site Improvements",
    "mechanicalelectrical": "Mechanical and Electrical",
    "dockseawall": "Dock and Seawall",
    "interiorbuilding": "Interior Building",
    "furnitureequipment": "Furniture and Equipment",
    "miscbuilding": "Misc Building",
    "furniturefixturesequipment": "Furniture, Fixtures and Equipment",
    "buildingpainting": "Building Painting",
    "interhallcommonarea": "Inter Hall Common Area",
    "roofreplacement": "Roof Replacement",
    "miscbuildingcomponents": "Misc Building"
}

def connect_to_mongo():
    """Connect to MongoDB and return client and collection"""
    try:
        client = pymongo.MongoClient(MONGO_URI)
        # Test connection
        client.admin.command('ping')
        logger.info("Successfully connected to MongoDB")
        return client, client[DATABASE_NAME][COLLECTION_NAME]
    except ConnectionFailure as e:
        logger.error(f"Could not connect to MongoDB: {str(e)}")
        raise

def safe_delete(filepath):
    """Safely delete a file with retries and garbage collection"""
    for attempt in range(5):
        try:
            os.unlink(filepath)
            return True
        except PermissionError:
            time.sleep(0.2)
            gc.collect()
    logger.warning(f"Could not delete temporary file {filepath}")
    return False

def get_current_date_formatted():
    """Return current date in format like 'January 4 2025' (without leading zero)"""
    now = datetime.now()
    return now.strftime("%B %-d %Y") if hasattr(now, 'strftime') and not is_windows() else now.strftime("%B %d %Y").replace(" 0", " ")

def is_windows():
    import platform
    return platform.system() == "Windows"

def format_usd(value):
    """Format values as USD currency strings"""
    try:
        # Handle strings with $ and/or commas gracefully
        cleaned = str(value).replace('$', '').replace(',', '').strip()
        number = float(cleaned)
        return "${:,.2f}".format(number)
    except (ValueError, TypeError):
        # If it already looks like a valid USD string, return it
        if isinstance(value, str) and value.strip().startswith('$'):
            return value.strip()
        return value or ""

def get_category_display_name(name):
    """Get a clean display name for a category"""
    normalized_key = re.sub(r'[^a-z0-9]', '', name.lower())
    if normalized_key in CATEGORY_TITLE_MAP:
        return CATEGORY_TITLE_MAP[normalized_key]

    is_component_category = re.search(r'(components|elements)$', name, re.IGNORECASE)

    has_ac = False
    if re.search(r'a/?c$', name, re.IGNORECASE):
        has_ac = True
        name = re.sub(r'/?a/?c$', '', name, flags=re.IGNORECASE)

    name = re.sub(r'(components|elements)$', '', name, flags=re.IGNORECASE)
    name = re.sub(r'([a-z])([A-Z])', r'\1 \2', name)
    name = re.sub(r'[_\-]+', ' ', name)
    name = re.sub(r'&', ' and ', name)
    name = re.sub(r'[^\w\s]', '', name)
    name = re.sub(r'\s+', ' ', name).strip()

    known_parts = [
        'equipmentroom', 'equipment', 'room', 'generator', 'softener', 'replacement',
        'valves', 'painting', 'safety', 'service', 'area', 'site', 'unit',
        'switch', 'meter', 'trashchute', 'compactor', 'chute', 'trash', 'elevator'
    ]

    def smart_split(word):
        previous = None
        while word != previous:
            previous = word
            for part in known_parts:
                match = re.search(f'([a-z]{{4,}})({part})$', word, re.IGNORECASE)
                if match:
                    word = re.sub(f'([a-z]{{4,}})({part})$', r'\1 \2', word, flags=re.IGNORECASE)
                    break
        return word

    name = ' '.join(smart_split(w) for w in name.split())
    name = name.title()

    if has_ac:
        name += ' A/C'

    if is_component_category and not re.search(r'Components$', name):
        name += ' Components'

    return name

def hide_spire_watermark(pdf_path: str, output_path: str):
    """Hide evaluation watermark from Spire PDF"""
    doc = fitz.open(pdf_path)

    for page_index, page in enumerate(doc):
        blocks = page.get_text("dict")["blocks"]

        for b in blocks:
            if b.get("type") == 0:  # Text block
                for line in b.get("lines", []):
                    for span in line.get("spans", []):
                        text = span.get("text", "").strip()
                        if "Evaluation Warning" in text:
                            rect = fitz.Rect(span["bbox"])

                            # Expand manually (e.g., 1 point padding all around)
                            rect.x0 -= 1
                            rect.y0 -= 1
                            rect.x1 += 1
                            rect.y1 += 1

                            # Only redact if it's near the top of the page
                            if rect.y1 < page.rect.height * 0.2:
                                page.add_redact_annot(rect, fill=(1, 1, 1))
        page.apply_redactions()

    doc.save(output_path)
    return output_path

# def hide_spire_watermark(pdf_path: str, output_path: str):
#     doc = fitz.open(pdf_path)

#     for page_index, page in enumerate(doc):
#         blocks = page.get_text("dict")["blocks"]

#         for b in blocks:
#             if b.get("type") == 0:  # Text block
#                 for line in b.get("lines", []):
#                     for span in line.get("spans", []):
#                         text = span.get("text", "").strip()
#                         if "Evaluation Warning" in text:
#                             rect = fitz.Rect(span["bbox"])
#                             print(f"Watermark rect: {rect}")

#                             # Expand manually (e.g., 1 point padding all around)
#                             rect.x0 -= 1
#                             rect.y0 -= 1
#                             rect.x1 += 1
#                             rect.y1 += 1

#                             # Only redact if it's near the top of the page
#                             if rect.y1 < page.rect.height * 0.2:
#                                 page.add_redact_annot(rect, fill=(1, 1, 1))

#         page.apply_redactions()

def extract_component_tables(table_data):
    """
    Extract component data from table_data which may have inconsistent structures.
    This function handles both array format and object format.
    """
    logger.info(f"Extracting component tables...")
    
    extracted = {}
    if not table_data or not isinstance(table_data, dict):
        logger.warning(f"Invalid table_data type: {type(table_data)}")
        return extracted
        
    for category, components in table_data.items():
        if not components:
            continue
            
        # Clean the category name for display
        category_display = get_category_display_name(category)
        
        extracted[category_display] = []
        
        # Handle list format (array of objects)
        if isinstance(components, list):
            logger.info(f"Processing array format for category: {category}")
            for component in components:
                if not isinstance(component, dict):
                    continue
                    
                # Try multiple possible field names
                component_name = (
                    component.get("reserveItem") or 
                    component.get("description") or 
                    component.get("name") or 
                    ""
                )
                
                useful_life = (
                    component.get("totalLife") or 
                    component.get("useful_life") or 
                    ""
                )
                
                remaining_life = (
                    component.get("lifeRemaining") or 
                    component.get("estimatedRemainingLifeYears") or 
                    ""
                )
                
                condition_grade = component.get("conditionGrade", "")
                
                if component_name:  # Only add component if it has a name
                    extracted[category_display].append({
                        "component": component_name,
                        "usefulLife": str(useful_life),
                        "lifeRemaining": str(remaining_life),
                        "conditionGrade": str(condition_grade)
                    })
        
        # Handle dictionary format (object with named keys)
        elif isinstance(components, dict):
            logger.info(f"Processing object format for category: {category}")
            for name, values in components.items():
                if not isinstance(values, dict):
                    # Handle case where the value itself is the component name
                    if isinstance(values, (str, int, float)):
                        extracted[category_display].append({
                            "component": str(name),
                            "usefulLife": "",
                            "lifeRemaining": "",
                            "conditionGrade": ""
                        })
                    continue
                
                # Get description or use key name
                component_name = values.get("description", name)
                
                useful_life = (
                    values.get("useful_life") or 
                    values.get("totalLife") or 
                    ""
                )
                
                remaining_life = (
                    values.get("estimatedRemainingLifeYears") or 
                    values.get("lifeRemaining") or 
                    ""
                )
                
                condition_grade = values.get("conditionGrade", "")
                
                extracted[category_display].append({
                    "component": component_name,
                    "usefulLife": str(useful_life),
                    "lifeRemaining": str(remaining_life),
                    "conditionGrade": str(condition_grade)
                })
        
        # Remove categories with no components
        if not extracted[category_display]:
            del extracted[category_display]
    
    logger.info(f"Extracted {len(extracted)} categories with components")
    return extracted

def calculate_overall_grade(grades):
    """Calculate overall grade from a list of individual grades"""
    valid_grades = [g for g in grades if g in ['A', 'B', 'C', 'D', 'E', 'F']]
    
    if not valid_grades:
        return "N/A"

    # Map grades to numeric values
    grade_values = {'A': 0, 'B': 1, 'C': 2, 'D': 3, 'E': 4, 'F': 5}
    numeric_grades = [grade_values[g] for g in valid_grades]
    
    # Calculate average and round to nearest grade
    average = sum(numeric_grades) / len(numeric_grades)
    rounded = round(average)
    
    # Map back to letter grade
    reverse_map = {0: 'A', 1: 'B', 2: 'C', 3: 'D', 4: 'E', 5: 'F'}
    grade = reverse_map[rounded]
    
    # Add symbol based on grade
    if grade in ['A', 'B']:
        return f"{grade} +"
    elif grade in ['C', 'D']:
        return f"{grade} !"
    elif grade in ['E', 'F']:
        return f"{grade} x"
    else:
        return grade

def format_report_link_urls(property_data):
    """Generate full S3 URLs for the report links."""
    # base_url = f"https://{S3_BUCKET_NAME}.s3.amazonaws.com/"
    base_url = f"https://fasstcondo-public-files.s3.us-east-1.amazonaws.com/"
    
    # Get the report keys
    sirs_key = property_data.get('sirsReportKey', '')
    milestone_key = property_data.get('milestoneReportKey', '')
    
    # Format full URLs if keys exist
    sirs_url = f"{base_url}{sirs_key}" if sirs_key else ""
    milestone_url = f"{base_url}{milestone_key}" if milestone_key else ""
    
    return sirs_url, milestone_url

def get_nested_value(obj, path, default=""):
    """Get a value from a nested object using dot notation path"""
    keys = path.split('.')
    result = obj
    
    for key in keys:
        if isinstance(result, dict) and key in result:
            result = result.get(key)
        else:
            return default
            
    # Convert to string or empty string if None
    return str(result) if result is not None else default

def format_data(property_doc):
    """Format property document data for PDF generation"""
    logger.info("Formatting property data for PDF generation")
    formatted_data = {}
    
    # Core property fields mapping
    fields = [
        ('addressKey', 'address'),
        ('heatedSpace', 'heatedSpace'),
        ('image_url', 'mediaUrl'),
        ('bedroom', 'beds'),
        ('bathroom', 'bathroom'),
        ('listPrice', 'askingPrice'),
        ('yearInBuilt', 'year'),
        ('milestoneReportAvailable', 'milestoneReportAvailable'),
        ('currentFee', 'regularAssessmentAmount'),
        ('sirsReportKey', 'sirsReportKey'),
        ('milestoneReportKey', 'milestoneReportKey'),
        ('currentReserve', 'sirsAiResponse.reserveFunds.currentReserveFunding.currentReserve'),
        ('requiredReserve', 'sirsAiResponse.reserveFunds.requiredReserveFunding.requiredReserve'),
        ('description', 'description'),
    ]
    
    # Populate all fields with nested value getter
    for output_key, input_key in fields:
        formatted_data[output_key] = get_nested_value(property_doc, input_key)
    
    # Special handling for potential fee
    formatted_data['potentialFee'] = get_nested_value(
        property_doc, 'sirsAiResponse.averageMonthlyContributionPerUnit'
    )
    
    # Calculate days on market from onMarketTimestamp
    try:
        on_market_str = property_doc.get('onMarketTimestamp', '')
        if on_market_str:
            on_market_date = datetime.strptime(on_market_str, "%Y-%m-%dT%H:%M:%S.%fZ").replace(tzinfo=timezone.utc)
            current_date = datetime.now(timezone.utc)
            days_diff = (current_date - on_market_date).days
            formatted_data['daysOnMarket'] = f"{days_diff} {'day' if days_diff == 1 else 'days'}"
        else:
            formatted_data['daysOnMarket'] = "0 days"
    except Exception as e:
        logger.error(f"Error calculating days on market: {e}")
        formatted_data['daysOnMarket'] = "N/A"
    
    # Calculate monthly increase percentage
    try:
        current_fee = float(str(get_nested_value(property_doc, 'regularAssessmentAmount', '0')).replace('$', '').replace(',', ''))
        potential_fee = float(str(formatted_data['potentialFee']).replace('$', '').replace(',', ''))
        
        if current_fee > 0:
            increase = ((potential_fee - current_fee) / current_fee) * 100
            formatted_data['monthlyInc'] = f"{increase:.1f}%"
        else:
            formatted_data['monthlyInc'] = "N/A"
    except Exception as e:
        logger.warning(f"Error calculating monthly increase: {str(e)}")
        formatted_data['monthlyInc'] = "N/A"

    # Calculate shortfall amount
    try:
        required = float(str(formatted_data['requiredReserve']).replace('$', '').replace(',', ''))
        current = float(str(formatted_data['currentReserve']).replace('$', '').replace(',', ''))
        shortfall = required - current
        formatted_data['shortfallAmount'] = str(shortfall)
    except Exception as e:
        logger.warning(f"Error calculating shortfall: {str(e)}")
        formatted_data['shortfallAmount'] = "0"

    # Calculate overall condition grade based on reserve ratio
    try:
        required = float(str(formatted_data['requiredReserve']).replace('$', '').replace(',', ''))
        current = float(str(formatted_data['currentReserve']).replace('$', '').replace(',', ''))
        
        if required > 0:
            ratio = current / required
            if ratio >= 0.98:
                grade = "A"
            elif ratio >= 0.9:
                grade = "B"
            elif ratio >= 0.75:
                grade = "C"
            elif ratio >= 0.5:
                grade = "D"
            elif ratio >= 0.25:
                grade = "E"
            else:
                grade = "F"
            formatted_data['overallConditionGrade'] = grade
        else:
            formatted_data['overallConditionGrade'] = "N/A"
    except Exception as e:
        logger.warning(f"Error calculating condition grade: {str(e)}")
        formatted_data['overallConditionGrade'] = "N/A"

    # Extract phase2 milestone data
    try:
        formatted_data['phase2Milestone'] = get_nested_value(
            property_doc, 'milestoneAiResponse.phase2MilestoneReportNeeded', 'N/A'
        )
    except Exception as e:
        logger.warning(f"Error extracting phase2Milestone: {str(e)}")
        formatted_data['phase2Milestone'] = "N/A"

    # Combine and deduplicate permits
    try:
        permit_details = property_doc.get('permitDetails', [])
        apt_permit_details = property_doc.get('entireAppartmnetPermitDetails', [])
        
        combined_permits = permit_details + apt_permit_details
        seen = set()
        unique_permits = []
        
        for permit in combined_permits:
            pid = permit.get("permitNumber")
            if pid and pid not in seen:
                seen.add(pid)
                unique_permits.append(permit)
                
        formatted_data['permitInformation'] = unique_permits
    except Exception as e:
        logger.warning(f"Error processing permits: {str(e)}")
        formatted_data['permitInformation'] = []

    # Extract and structure dynamic table data
    table_data = property_doc.get('tableData', {})
    formatted_data['dynamicTables'] = extract_component_tables(table_data)

    # Compute overall grade from dynamic tables
    try:
        all_grades = [
            comp.get('conditionGrade')
            for comps in formatted_data['dynamicTables'].values()
            for comp in comps 
            if comp.get('conditionGrade') and comp.get('conditionGrade') in ['A', 'B', 'C', 'D', 'E', 'F']
        ]
        
        formatted_data['overallGrade'] = calculate_overall_grade(all_grades) if all_grades else "N/A"
    except Exception as e:
        logger.warning(f"Error calculating overall grade: {str(e)}")
        formatted_data['overallGrade'] = "N/A"

    return formatted_data

def create_permit_table_with_reportlab(permit_data, filename):
    """Create a PDF page with permit table data using improved table formatting"""
    # Document setup with proper page size
    doc = BaseDocTemplate(
        filename,
        pagesize=(446.25, 631.5),
        leftMargin=50,
        rightMargin=50,
        topMargin=60,
        bottomMargin=60
    )

    # Content frame for layout control
    frame = Frame(
        doc.leftMargin,
        doc.bottomMargin,
        doc.width,
        doc.height,
        leftPadding=0,
        rightPadding=0,
        topPadding=0,
        bottomPadding=0,
        id='normal'
    )

    # Add page number function
    def add_page_number(canvas, doc):
        page_num = canvas.getPageNumber()
        if page_num >= 7:  # Adjust based on when logical pages should start
            logical_page = page_num - 4  # Adjust based on your template
            canvas.saveState()
            canvas.setFont('Helvetica', 10)
            canvas.drawRightString(doc.pagesize[0] - 40, 15, f"Page {logical_page}")
            canvas.restoreState()

    # Add page template with footer
    doc.addPageTemplates([
        PageTemplate(id='PermitTable', frames=frame, onPageEnd=add_page_number)
    ])

    elements = []
    styles = getSampleStyleSheet()
    
    # Create orange heading style
    orange_heading = ParagraphStyle(
        'OrangeHeading',
        parent=styles['Heading2'],
        textColor=colors.orange,
        fontName='Helvetica-Bold',
        fontSize=12,
        spaceAfter=6
    )

    # Table settings
    headers = ["Permit Number", "Description", "Issued Date", "Estimated Value"]
    col_widths = [1.2 * inch, 2.2 * inch, 1.0 * inch, 1.3 * inch]
    max_rows_per_page = 15

    # Split data into pages for better pagination
    for start in range(0, len(permit_data), max_rows_per_page):
        chunk = permit_data[start:start + max_rows_per_page]
        table_data = [headers]

        for permit in chunk:
            table_data.append([
                permit.get("permitNumber", ""),
                permit.get("description", ""),
                permit.get("issueDate", ""),
                format_usd(permit.get("estimatedValue", ""))
            ])

        table = Table(table_data, colWidths=col_widths, repeatRows=1, hAlign='CENTER')
        
        # Apply consistent style
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor("#f0f0f0")),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.darkblue),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('LINEBELOW', (0, 0), (-1, 0), 1, colors.darkgray),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 8),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('ALIGN', (3, 1), (3, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 9),
            ('TOPPADDING', (0, 0), (-1, -1), 9),
        ]))

        # Add spacing and title
        elements.append(Spacer(1, 0.5 * inch))
        elements.append(Paragraph("Permits", orange_heading))
        elements.append(Spacer(1, 0.2 * inch))
        elements.append(table)

        # Add page break if more data exists
        if start + max_rows_per_page < len(permit_data):
            elements.append(PageBreak())

    # Build the document
    doc.build(elements)
    return filename

def build_exterior_table(building_data, filename, title=None):
    """Create a PDF page with component tables using improved formatting"""
    styles = getSampleStyleSheet()
    styleN = styles["BodyText"]

    # Use BaseDocTemplate for better layout control
    doc = BaseDocTemplate(
        filename,
        pagesize=(446.25, 631.5),
        leftMargin=50,
        rightMargin=50,
        topMargin=60,
        bottomMargin=60
    )

    # Add content frame
    frame = Frame(
        doc.leftMargin,
        doc.bottomMargin,
        doc.width,
        doc.height,
        leftPadding=0,
        rightPadding=0,
        topPadding=0,
        bottomPadding=0,
        id='normal'
    )

    # Add page number function
    def add_page_number(canvas, doc):
        page_num = canvas.getPageNumber()
        if page_num >= 5:  # Adjust based on your template
            logical_page = page_num - 2  # Adjust for your page numbering
            canvas.saveState()
            canvas.setFont('Helvetica', 10)
            canvas.drawRightString(doc.pagesize[0] - 40, 15, f"Page {logical_page}")
            canvas.restoreState()

    # Add page template
    doc.addPageTemplates([PageTemplate(id='ExteriorTable', frames=frame, onPageEnd=add_page_number)])

    elements = []

    # Create consistent orange heading style
    orange_heading = ParagraphStyle(
        'OrangeHeading',
        parent=styles['Heading2'],
        textColor=colors.orange,
        fontName='Helvetica-Bold',
        fontSize=12,
        spaceAfter=6
    )

    # Add title if provided
    if title:
        elements.append(Paragraph(title, orange_heading))
        elements.append(Spacer(1, 6))

    # Define grade symbols
    symbol_map = {"A": "+", "B": "+", "C": "!", "D": "!", "E": "x", "F": "x"}

    # Create table with headers
    data = [["Building Element", "Total Life", "Life Remaining", "Condition Grade"]]

    # Add component rows
    for item in building_data:
        grade = item.get("conditionGrade", "")
        grade_symbol = f"{grade} {symbol_map.get(grade, '?')}"
        data.append([
            Paragraph(item.get("component", ""), styleN), 
            item.get("usefulLife", ""),
            item.get("lifeRemaining", ""),
            grade_symbol
        ])

    # Define column widths
    col_widths = [2.0 * inch, 1.2 * inch, 1.0 * inch, 1.3 * inch]

    # Define table style
    table_style = TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor("#f0f0f0")),
        ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 8),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('ALIGN', (0, 1), (0, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('TOPPADDING', (0, 0), (-1, -1), 8),
        ('LEFTPADDING', (0, 0), (-1, -1), 9),
        ('RIGHTPADDING', (0, 0), (-1, -1), 9),
    ])

    # Function to split data into chunks for pagination
    def split_data(data_list, max_rows):
        header = data_list[0]
        rows = data_list[1:]
        for i in range(0, len(rows), max_rows):
            yield [header] + rows[i:i + max_rows]

    # Process data in chunks for better pagination
    max_rows_per_page = 16
    chunks = list(split_data(data, max_rows=max_rows_per_page))

    for i, chunk in enumerate(chunks):
        table = Table(chunk, colWidths=col_widths, repeatRows=1, hAlign='CENTER')
        table.setStyle(table_style)
        elements.append(table)

        # Add page break if more chunks exist
        if i < len(chunks) - 1:
            elements.append(PageBreak())

    # Build the document
    doc.build(elements)
    return filename

def generate_report(property_data, input_pdf, output_s3_key):
    """
    Generate a PDF report using the template and property data
    
    Args:
        property_data (dict): Formatted property data
        input_pdf (str): Path to template PDF
        output_s3_key (str): S3 key for the output PDF
    """
    temp_files = []  # Track all temporary files for cleanup
    doc = None
    new_doc = None
    
    try:
        logger.info(f"Generating report using template: {input_pdf}")
        
        # --- PHASE 1: Initial Processing with Spire PDF ---
        doc = PdfDocument()
        doc.LoadFromFile(input_pdf)

        # Process all pages and replace text placeholders
        for i in range(doc.Pages.Count):
            page = doc.Pages[i]
            replacer = PdfTextReplacer(page)
            try:
                # Basic property information
                replacer.ReplaceAllText("addressKey", property_data.get('addressKey', ''))
                # replacer.ReplaceAllText("heatedSpace", property_data.get('heatedSpace', ''))
                replacer.ReplaceAllText("heatedSpace", f"{property_data.get('heatedSpace', '')} sqft")
                replacer.ReplaceAllText("bedroom", property_data.get('bedroom', ''))
                replacer.ReplaceAllText("bathroom", property_data.get('bathroom', ''))
                replacer.ReplaceAllText("listPrice", property_data.get('listPrice', ''))
                replacer.ReplaceAllText("daysOnMarcket", property_data.get('daysOnMarket', ''))
                replacer.ReplaceAllText("yearInBuilt", property_data.get('yearInBuilt', ''))
                replacer.ReplaceAllText("DateofFileCreation", get_current_date_formatted())
                
                # Financial information - properly formatted
                try:
                    current_fee = float(str(property_data.get('currentFee', '0')).replace('$', '').replace(',', ''))
                    replacer.ReplaceAllText("$currentFee", "${:,.2f}".format(current_fee))
                except:
                    replacer.ReplaceAllText("$currentFee", "$0.00")

                try:
                    potential_fee = float(str(property_data.get('potentialFee', '0')).replace('$', '').replace(',', ''))
                    replacer.ReplaceAllText("$potentialFee", "${:,.2f}".format(potential_fee))
                except:
                    replacer.ReplaceAllText("$potentialFee", "$0.00")

                replacer.ReplaceAllText("monthlyInc%", property_data.get('monthlyInc', 'N/A'))
                replacer.ReplaceAllText("overallGrade", property_data.get('overallGrade', 'N/A'))
                
                # # Page 2 information
                # replacer.ReplaceAllText("milestoneReportAvailable", property_data.get('milestoneReportAvailable', 'No'))
                # Page 2 information
                try:
                    # Handle milestone report availability text replacement
                    milestone_value = property_data.get('milestoneReportAvailable', None)
                    if milestone_value is None or milestone_value == '':
                        # If key doesn't exist or value is empty, explicitly set to 'No'
                        replacer.ReplaceAllText("milestoneReportAvailable", 'No')
                    else:
                        # Use the value from property_data
                        replacer.ReplaceAllText("milestoneReportAvailable", milestone_value)
                    
                    logger.info(f"Set milestoneReportAvailable to: {property_data.get('milestoneReportAvailable', 'No')}")
                except Exception as e:
                    logger.error(f"Error replacing milestone report availability text: {e}")
                    # Ensure a default value is set even if an error occurs
                    try:
                        replacer.ReplaceAllText("milestoneReportAvailable", 'No')
                    except:
                        pass
                replacer.ReplaceAllText("phase2Milestone", property_data.get('phase2Milestone', 'N/A'))
                # replacer.ReplaceAllText("description", property_data.get('description', ''))
                
                # Reserve information
                try:
                    current_reserve = float(str(property_data.get('currentReserve', '0')).replace('$', '').replace(',', ''))
                    replacer.ReplaceAllText("$currentReserve", "${:,.2f}".format(current_reserve))
                except:
                    replacer.ReplaceAllText("$currentReserve", "$0.00")

                try:
                    required_reserve = float(str(property_data.get('requiredReserve', '0')).replace('$', '').replace(',', ''))
                    replacer.ReplaceAllText("$requiredReserve", "${:,.2f}".format(required_reserve))
                except:
                    replacer.ReplaceAllText("$requiredReserve", "$0.00")

                try:
                    shortfall = float(str(property_data.get('shortfallAmount', '0')).replace('$', '').replace(',', ''))
                    replacer.ReplaceAllText("$shortfallAmount", "${:,.2f}".format(shortfall))
                except:
                    replacer.ReplaceAllText("$shortfallAmount", "$0.00")

                replacer.ReplaceAllText("overallConditionGrade", property_data.get('overallConditionGrade', 'N/A'))
                
            except Exception as e:
                logger.error(f"Error replacing placeholders: {str(e)}")
        
        # Handle the property image
        try:
            image_url = property_data.get('image_url')
            if image_url:
                response = requests.get(image_url)
                image_pil = Image.open(BytesIO(response.content))

                with tempfile.NamedTemporaryFile(delete=False, suffix='.jpeg') as temp_file:
                    temp_image_path = temp_file.name
                    temp_files.append(temp_image_path)  # Track for cleanup
                    image_pil.save(temp_image_path)

                # Load the image into the PDF
                original_image_spire = PdfImage.FromFile(temp_image_path)
                imageHelper = PdfImageHelper()
                
                # Handle Page 1 (main image)
                page1_images = imageHelper.GetImagesInfo(doc.Pages[1])
                if page1_images and len(page1_images) > 0:
                    # Find the largest image on the page (likely the main image)
                    background_image = max(
                        page1_images, 
                        key=lambda img: img.Bounds.Width * img.Bounds.Height
                    )
                    imageHelper.ReplaceImage(background_image, original_image_spire)
                
                # Handle Page 2 (may also need the image)
                if doc.Pages.Count > 1:
                    page2_images = imageHelper.GetImagesInfo(doc.Pages[2])
                    if page2_images and len(page2_images) > 0:
                        imageHelper.ReplaceImage(page2_images[0], original_image_spire)
        except Exception as e:
            logger.error(f"Error processing property image: {str(e)}")
        
        # Save intermediate PDF for further processing
        intermediate_spire_path = "/tmp/intermediate_spire.pdf"
        cleaned_pdf_path = "/tmp/intermediate_clean.pdf" 
        temp_files.extend([intermediate_spire_path, cleaned_pdf_path])
        doc.SaveToFile(intermediate_spire_path)
        doc.Close()
        doc = None  # Release resource
        
        # Remove Spire watermark
        hide_spire_watermark(intermediate_spire_path, cleaned_pdf_path)
        
        # --- PHASE 2: Dynamic Content with PyMuPDF ---
        # Create new output document
        new_doc = fitz.open()
        
        # First add all original pages
        with fitz.open(cleaned_pdf_path) as src_doc:
            new_doc.insert_pdf(src_doc)

        # Process permit table if exists
        if property_data.get('permitInformation'):
            # Remove static permit page if it exists (assumed at index 4)
            if len(new_doc) > 4:
                new_doc.delete_page(4)
                logger.info("Removed static permit placeholder page")

            # Generate dynamic permit table
            permit_pdf = tempfile.mktemp(suffix='.pdf')
            temp_files.append(permit_pdf)
            create_permit_table_with_reportlab(property_data['permitInformation'], permit_pdf)

            try:
                with fitz.open(permit_pdf) as permit_doc:
                    if permit_doc.page_count > 0:
                        # Insert at index 4 or append if not enough pages
                        insert_position = min(4, len(new_doc))
                        new_doc.insert_pdf(permit_doc, from_page=0, to_page=permit_doc.page_count-1, start_at=insert_position)
                        logger.info(f"Inserted permit pages at index {insert_position}")
            except Exception as e:
                logger.error(f"Error processing permit table: {e}")
        else:
            # No permit data: also remove static page if it exists
            if len(new_doc) > 4:
                new_doc.delete_page(4)
                logger.info("No permit data, removed static permit page")

        # Process dynamic tables
        for category, table_data in property_data.get('dynamicTables', {}).items():
            if not table_data:
                continue
                
            table_pdf = tempfile.mktemp(suffix='.pdf')
            temp_files.append(table_pdf)
            
            # Use proper category display name
            category_title = get_category_display_name(category)
            build_exterior_table(table_data, table_pdf, title=category_title)
            
            try:
                with fitz.open(table_pdf) as table_doc:
                    if table_doc.page_count > 0:
                        new_doc.insert_pdf(table_doc)
                        logger.info(f"Added dynamic table: {category_title}")
            except Exception as e:
                logger.error(f"Error processing dynamic table {category}: {e}")

        # # Process hyperlinks on the request page (page 4)
        # try:
        #     # Get properly formatted S3 URLs
        #     sirs_url, milestone_url = format_report_link_urls(property_data)
            
        #     if len(new_doc) > 3:
        #         request_page = new_doc[3]
                
        #         # Milestone report link
        #         milestone_instances = request_page.search_for("RequestMilestoneReport")
        #         if milestone_instances and milestone_url:
        #             rect = milestone_instances[0]
        #             # Clear existing placeholder text
        #             request_page.insert_textbox(rect, "", fontsize=10, color=(1, 1, 1), fill=(1, 1, 1))
                    
        #             # Add clickable link
        #             request_page.insert_link({
        #                 "from": rect,
        #                 "uri": milestone_url,
        #                 "kind": fitz.LINK_URI,
        #                 "border": [0, 0, 0],
        #                 "color": (0, 0, 1)
        #             })
        #             logger.info(f"Milestone report link inserted with URL: {milestone_url}")

        #         # SIRS report link
        #         sirs_instances = request_page.search_for("RequestSirsReport")
        #         if sirs_instances and sirs_url:
        #             rect = sirs_instances[0]
        #             # Clear placeholder
        #             request_page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                    
        #             # Insert clickable link
        #             request_page.insert_link({
        #                 "from": rect,
        #                 "uri": sirs_url,
        #                 "kind": fitz.LINK_URI,
        #                 "border": [0, 0, 0],
        #                 "color": (0, 0, 1)
        #             })
                    
        #             # Add text label (with better y-position)
        #             y_offset = 6
        #             request_page.insert_text(
        #                 (rect.x0, rect.y0 + y_offset),
        #                 "RequestSirsReport", 
        #                 fontsize=8, 
        #                 color=(0, 0, 0)
        #             )
        #             logger.info(f"SIRS report link inserted with URL: {sirs_url}")
        # except Exception as e:
        #     logger.error(f"Error processing hyperlinks: {e}")




        # # Process hyperlinks on the request page (page 4)
        # try:
        #     # Get properly formatted S3 URLs
        #     sirs_url, milestone_url = format_report_link_urls(property_data)
            
        #     if len(new_doc) > 3:
        #         request_page = new_doc[3]
                
        #         # Milestone report link
        #         milestone_instances = request_page.search_for("RequestMilestoneReport")
        #         if milestone_instances:
        #             rect = milestone_instances[0]
        #             if milestone_url:
        #                 # Clear existing placeholder text
        #                 request_page.insert_textbox(rect, "", fontsize=10, color=(1, 1, 1), fill=(1, 1, 1))
                        
        #                 # Add clickable link
        #                 request_page.insert_link({
        #                     "from": rect,
        #                     "uri": milestone_url,
        #                     "kind": fitz.LINK_URI,
        #                     "border": [0, 0, 0],
        #                     "color": (0, 0, 1)
        #                 })
                        
        #                 # Add user-friendly text for the link
        #                 y_offset = 6
        #                 request_page.insert_text(
        #                     (rect.x0, rect.y0 + y_offset),
        #                     "Project Milestone Report", 
        #                     fontsize=8, 
        #                     color=(0, 0, 1)
        #                 )
        #                 logger.info(f"Milestone report link inserted with URL: {milestone_url}")
        #             else:
        #                 # If no URL, remove the entire key text by drawing white rectangle over it
        #                 request_page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
        #                 logger.info("Milestone report key removed (no URL available)")

        #         # SIRS report link
        #         sirs_instances = request_page.search_for("RequestSirsReport")
        #         if sirs_instances:
        #             rect = sirs_instances[0]
        #             if sirs_url:
        #                 # Clear placeholder
        #                 request_page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                        
        #                 # Insert clickable link
        #                 request_page.insert_link({
        #                     "from": rect,
        #                     "uri": sirs_url,
        #                     "kind": fitz.LINK_URI,
        #                     "border": [0, 0, 0],
        #                     "color": (0, 0, 1)
        #                 })
                        
        #                 # Add user-friendly text for the link
        #                 y_offset = 6
        #                 request_page.insert_text(
        #                     (rect.x0, rect.y0 + y_offset),
        #                     "SIRS Inspection Report", 
        #                     fontsize=8, 
        #                     color=(0, 0, 1)
        #                 )
        #                 logger.info(f"SIRS report link inserted with URL: {sirs_url}")
        #             else:
        #                 # If no URL, remove the entire key text by drawing white rectangle over it
        #                 request_page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
        #                 logger.info("SIRS report key removed (no URL available)")
        # except Exception as e:
        #     logger.error(f"Error processing hyperlinks: {e}")

        # Process hyperlinks on the request page (page 4)
        try:
            # Get properly formatted S3 URLs
            sirs_url, milestone_url = format_report_link_urls(property_data)
            
            if len(new_doc) > 3:
                request_page = new_doc[3]
                
                # Milestone report link
                milestone_instances = request_page.search_for("RequestMilestoneReport")
                if milestone_instances:
                    rect = milestone_instances[0]
                    
                    # Properly clear the existing text by drawing a white rectangle
                    request_page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                    
                    if milestone_url:
                        # Add clickable link
                        request_page.insert_link({
                            "from": rect,
                            "uri": milestone_url,
                            "kind": fitz.LINK_URI,
                            "border": [0, 0, 0],
                            "color": (0, 0, 1)
                        })
                        
                        # Add visible link text
                        request_page.insert_text(
                            (rect.x0, rect.y0 + 6),
                            "RequestMilestoneReport", 
                            fontsize=8, 
                            color=(0, 0, 1)
                        )
                        logger.info(f"Milestone report link inserted with URL: {milestone_url}")
                    else:
                        # Replace with "Not Uploaded" text
                        request_page.insert_text(
                            (rect.x0, rect.y0 + 6),
                            "Not Uploaded", 
                            fontsize=8, 
                            color=(0, 0, 0)
                        )
                        logger.info("Milestone report marked as Not Uploaded (no URL available)")

                # SIRS report link
                sirs_instances = request_page.search_for("RequestSirsReport")
                if sirs_instances:
                    rect = sirs_instances[0]
                    
                    # Properly clear the existing text by drawing a white rectangle
                    request_page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                    
                    if sirs_url:
                        # Insert clickable link
                        request_page.insert_link({
                            "from": rect,
                            "uri": sirs_url,
                            "kind": fitz.LINK_URI,
                            "border": [0, 0, 0],
                            "color": (0, 0, 1)
                        })
                        
                        # Add visible link text
                        request_page.insert_text(
                            (rect.x0, rect.y0 + 6),
                            "SIRS Inspection Report", 
                            fontsize=8, 
                            color=(0, 0, 1)
                        )
                        logger.info(f"SIRS report link inserted with URL: {sirs_url}")
                    else:
                        # Replace with "Not Uploaded" text
                        request_page.insert_text(
                            (rect.x0, rect.y0 + 6),
                            "Not Uploaded", 
                            fontsize=8, 
                            color=(0, 0, 0)
                        )
                        logger.info("SIRS report marked as Not Uploaded (no URL available)")
        except Exception as e:
            logger.error(f"Error processing hyperlinks: {e}")

        # --- PHASE 3: Save final PDF ---
        output_temp_path = f"/tmp/{output_s3_key.split('/')[-1]}"
        temp_files.append(output_temp_path)
        new_doc.save(output_temp_path)
        new_doc.close()
        new_doc = None
        
        # Upload to S3
        try:
            s3_client = boto3.client('s3')
            s3_client.upload_file(output_temp_path, S3_BUCKET_NAME, output_s3_key)
            logger.info(f"Successfully uploaded report to S3: {output_s3_key}")
            return True
        except Exception as e:
            logger.error(f"Error uploading PDF to S3: {str(e)}")
            return False
            
    except Exception as e:
        logger.error(f"Error generating report: {str(e)}")
        logger.error(traceback.format_exc())
        return False
    finally:
        # Ensure documents are closed
        if doc is not None:
            try:
                doc.Close()
            except:
                pass
        if new_doc is not None:
            try:
                new_doc.close()
            except:
                pass
            
        # Cleanup all temporary files
        for path in temp_files:
            if os.path.exists(path):
                safe_delete(path)

def handler(event, context):
    """
    Lambda function handler to process incoming requests
    
    Args:
        event: Lambda event object
        context: Lambda context object
        
    Returns:
        API Gateway compatible response
    """
    logger.info(f"Received event: {json.dumps(event)}")
    
    # Connect to MongoDB
    try:
        client, db = connect_to_mongo()
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({'error': 'Database connection error'}),
            'headers': common_headers,
        }
    
    try:
        # Extract request body
        if 'body' in event:
            if isinstance(event['body'], str):
                body = json.loads(event['body'])
            else:
                body = event['body']
        else:
            body = event
        
        # Get property ID (support both 'id' and '_id')      (new)
        property_id = body.get('id') or body.get('_id')
        address = body.get('address')  # new input support
        result = None

        # Try to find by ID (old behavior — stays intact)
        if property_id:
            logger.info(f"Looking up property with ID: {property_id}")
            try:
                object_id = ObjectId(property_id)
                result = db.find_one({"_id": object_id})
            except Exception as e:
                logger.error(f"Error querying database by ID: {str(e)}")
                return {
                    'statusCode': 500,
                    'body': json.dumps({"message": f"Database error: {str(e)}"}),
                    'headers': common_headers,
                }

        # Fallback to address search if result not found
        if not result and address:
            logger.info(f"Trying address-based lookup: {address}")
            try:
                address = address.strip()  # Clean whitespace
                result = db.find_one({"address": {"$regex": address, "$options": "i"}})

            except Exception as e:
                logger.error(f"Error querying database by address: {str(e)}")
                return {
                    'statusCode': 500,
                    'body': json.dumps({"message": f"Database error: {str(e)}"}),
                    'headers': common_headers,
                }

        #  Return error if nothing is found
        if not result:
            logger.error("Property not found with ID or address")
            return {
                'statusCode': 404,
                'body': json.dumps({"message": "Property not found in database"}),
                'headers': common_headers,
            }
        
        property_id = str(result.get('_id'))  # <- this sets the correct ID for filename

        logger.info("Property found successfully.")
        
        logger.info(f"Found property with ID: {property_id}")
        
        # Format data from the database
        formatted_data = format_data(result)

        if not formatted_data.get("sirsReportKey"):
            logger.warning("SIRS report not available for this property.")
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': "SIRS report is not available for this property.",
                    'status': False
                }),
                'headers': common_headers,
            }
        
        #  Stop report generation if dynamic table data is missing or empty         
        if not formatted_data.get("dynamicTables"):
            logger.warning("No dynamic table data found. Skipping report generation.")
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': "The property doesn't contain enough data to generate FASST report.",
                    'status': False
                }),
                'headers': common_headers,
            }          
     
        
        # Define S3 paths
        s3_template_key = 'template_fasst.pdf'  # Template file name in S3
        output_s3_key = f'reports/{property_id}.pdf'  # Output file path in S3
        
        # Download the template from S3
        try:
            s3_client = boto3.client('s3')
            s3_response = s3_client.get_object(Bucket=S3_BUCKET_NAME, Key=s3_template_key)
            pdf_content = s3_response['Body'].read()
            
            # Save template to temp file
            temp_template_path = '/tmp/template.pdf'
            with open(temp_template_path, 'wb') as f:
                f.write(pdf_content)
        except Exception as e:
            logger.error(f"Error downloading template from S3: {str(e)}")
            return {
                'statusCode': 500,
                'body': json.dumps({'error': f"Template download error: {str(e)}"}),
                'headers': common_headers,
            }
        
        # Generate the report
        success = generate_report(formatted_data, temp_template_path, output_s3_key)
        
        # Clean up the template file
        safe_delete(temp_template_path)
        
        if not success:
            return {
                'statusCode': 500,
                'body': json.dumps({'error': 'Failed to generate report'}),
                'headers': common_headers,
            }
        
        # Generate a pre-signed URL for downloading the report
        try:
            # presigned_url = s3_client.generate_presigned_url(
            #     'get_object',
            #     Params={
            #         'Bucket': S3_BUCKET_NAME,
            #         'Key': output_s3_key
            #     },
            #     ExpiresIn=3600  # URL expires in 1 hour
            # )
            # Allow dashboard to request inline view via 'inline' flag
            inline_flag = body.get("inline", False)

            # If explicitly set, use inline; otherwise default to attachment
            disposition = "inline" if inline_flag else "attachment"


            #  This part uses the right behavior based on the request
            presigned_url = s3_client.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': S3_BUCKET_NAME,
                    'Key': output_s3_key,
                    'ResponseContentDisposition': disposition,
                    'ResponseContentType': 'application/pdf'
                },
                ExpiresIn=3600
            )

        except Exception as e:
            logger.error(f"Error generating presigned URL: {str(e)}")
            presigned_url = None
        
        # Return success response
        return {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'PDF processing successful',
                'outputKey': output_s3_key,
                'downloadUrl': presigned_url,
                'status': True
            }),
            'headers': common_headers,
        }
        
    except Exception as e:
        logger.error(f"Unhandled exception: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'statusCode': 500,
            'body': json.dumps({'error': str(e)}),
            'headers': common_headers,
        }
    finally:
        # Close MongoDB connection
        if 'client' in locals():
            client.close()