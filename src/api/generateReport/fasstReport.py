import fitz
import json
import logging
import boto3
from botocore.exceptions import Client<PERSON>rror, NoCredentialsError
from tqdm import tqdm
import pymongo
from pymongo.errors import ConnectionFailure
from datetime import datetime
from spire.pdf.common import *
from spire.pdf import *
import traceback
from PIL import Image, ImageFilter
import tempfile
import requests
from io import BytesIO
import os
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle
from reportlab.lib.units import inch
import tempfile




logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
MONGO_URI = "mongodb://mongdbuser:%266XR%24a%25jS2W5z%5E%40%24@3.83.138.34:27017/admin" 
DATABASE_NAME = "Fasst"
COLLECTION_NAME = "PropFromBridge"
common_headers = {
    "Access-Control-Allow-Headers": "Content-Type",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "OPTIONS,POST",
}

def connect_to_mongo():
    try:
        client = pymongo.MongoClient(MONGO_URI)
        return client, client[DATABASE_NAME][COLLECTION_NAME]
    except ConnectionFailure:
        logging.error("Could not connect to MongoDB Atlas")
        raise

def format_date(date):
    dt = datetime.strptime(date, "%Y-%m-%dT%H:%M:%S.%fZ")
    formatted_date = dt.strftime("%m/%d/%Y")
    return formatted_date

def create_permit_table_with_reportlab(permit_data, filename):
    """Create a PDF with a permit table using reportlab."""
    doc = SimpleDocTemplate(filename, pagesize=letter)
    elements = []
    
    # Create header row
    headers = ["Permit Number", "Description", "Issued Date", "Estimated Value"]
    table_data = [headers]
    
    # Add permit data rows
    for permit in permit_data:
        row = [
            permit["permitNumber"],
            permit["description"],
            permit["issueDate"],
            permit["estimatedValue"]
        ]
        table_data.append(row)
    
    # Calculate column widths - adjust as needed
    # Define column widths to match the image layout
    col_widths = [1.8*inch, 3.5*inch, 1.5*inch, 1.5*inch]

    # Create the table
    table = Table(table_data, colWidths=col_widths)

    # Define styles
    style = TableStyle([
        ('ALIGN', (0, 0), (-1, 0), 'LEFT'),  # Left align headers
        ('ALIGN', (0, 1), (0, -1), 'LEFT'),  # Left align permit numbers
        ('ALIGN', (1, 1), (1, -1), 'LEFT'),  # Left align description
        ('ALIGN', (2, 1), (2, -1), 'CENTER'),  # Center align issued date
        ('ALIGN', (3, 1), (3, -1), 'RIGHT'),  # Right align estimated value

        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),  # Bold header
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),

        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ('TOPPADDING', (0, 0), (-1, -1), 6),

        ('LINEBELOW', (0, 0), (-1, 0), 1, colors.lightgrey),  # Bottom border for header
        ('GRID', (0, 1), (-1, -1), 0.5, colors.lightgrey),  # Light grid lines
    ])
    
    table.setStyle(style)
    elements.append(table)
    
    # Build the document
    doc.build(elements)
    
    return filename


def generate_report(property,input_pdf,output_s3_key):
    try:
        doc = PdfDocument()
        doc.LoadFromFile(input_pdf)

        for i in range(doc.Pages.Count):
            page = doc.Pages[i]
            replacer = PdfTextReplacer(page)
            try:
                
                # page 1
                replacer.ReplaceAllText("addressKey",property['addressKey'])
                replacer.ReplaceAllText("heatedSpace",property['heatedSpace'])
                replacer.ReplaceAllText("bedroom",property['bedroom'])
                replacer.ReplaceAllText("bathroom",property['bathroom'])
                replacer.ReplaceAllText("listPrice",property['listPrice'])
                replacer.ReplaceAllText("daysOnMarcket",property['daysOnMarket'])
                replacer.ReplaceAllText("yearInBuilt",property['yearInBuilt'])
                replacer.ReplaceAllText("currentFee",property['currentFee'])
                # replacer.ReplaceAllText("potentialFee",property['potentialFee'])
                # replacer.ReplaceAllText("monthlyInc","22.12")
                
            # pag2
            
                replacer.ReplaceAllText("milestoneReportAvailable",property['milestoneReportAvailable'])
                replacer.ReplaceAllText("phase2Milestone",property['phase2Milestone'])
                # replacer.ReplaceAllText("requiredReserve","450000")
                # replacer.ReplaceAllText("shortfallAmount","650000")
                # replacer.ReplaceAllText("currentReserve","550000")
                
                
                # Exterior Building Elements
                
                # Life Remaining
                replacer.ReplaceAllText('r11',property['r11'])
                replacer.ReplaceAllText('r21',property['r21'])
                replacer.ReplaceAllText('r31',property['r31'])
                replacer.ReplaceAllText('r41',property['r41'])
                replacer.ReplaceAllText('r51',property['r51'])
                replacer.ReplaceAllText('r61',property['r61'])
                replacer.ReplaceAllText('r71',property['r71'])
                replacer.ReplaceAllText('r81',property['r81'])
                replacer.ReplaceAllText('r91',property['r91'])
                replacer.ReplaceAllText('r101',property['r101'])
                replacer.ReplaceAllText('r111',property['r111'])
                # replacer.ReplaceAllText('r121',property['r121'])
                
                
                # Building Services Elements
                replacer.ReplaceAllText('b11',property['b11'])
                replacer.ReplaceAllText('b21',property['b21'])
                replacer.ReplaceAllText('b31',property['b31'])
                replacer.ReplaceAllText('b41',property['b41'])
                replacer.ReplaceAllText('b51',property['b51'])
                replacer.ReplaceAllText('b61',property['b61'])
                
                
                
                

                
                
                
                

                
            
        
                
            except Exception as e:
                print(str(e))

        response = requests.get(property['image_url'])
        image_pil = Image.open(BytesIO(response.content))




        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpeg') as temp_orig_file:
            temp_orig_path = temp_orig_file.name
            image_pil.save(temp_orig_path)

        original_image_spire = PdfImage.FromFile(temp_orig_path)

        imageHelper = PdfImageHelper()
        imageInfo = imageHelper.GetImagesInfo(doc.Pages[1])
        imageInfo2 = imageHelper.GetImagesInfo(doc.Pages[2])

        
        # Find the background image (often the first one or the largest one)
        background_image_index = 0  # Assuming the background is the first image
        for i, img in enumerate(imageInfo):
            # You might need to identify the background image based on position or size
            # For example, if it's the largest image based on bounds:
            if i > 0 and (img.Bounds.Width * img.Bounds.Height) > (imageInfo[background_image_index].Bounds.Width * imageInfo[background_image_index].Bounds.Height):
                background_image_index = i

        # Replace the background image
        if len(imageInfo) > 0:
            imageHelper.ReplaceImage(imageInfo[background_image_index], original_image_spire)
        if len(imageInfo2) > 0:
            imageHelper.ReplaceImage(imageInfo2[0], original_image_spire)
            
        doc.SaveToFile("/tmp/output1.pdf")
    
        doc = fitz.open("/tmp/output1.pdf")
        
        condition_mapping = {
        "A": ("green", "+"),
        "B": ("green", "+"),
        "C": ("yellow", "!"),
        "D": ("yellow", "!"),
        "E": ("red", "x"),
        "F": ("red", "x")
    }
    
    # Color mapping for PyMuPDF
        color_mapping = {
        "green": (0, 1, 0),
        "yellow": (1, 1, 0),
        "red": (1, 0, 0),
        "black": (0, 0, 0)
    }
        def update_text_field(page, search_text, grade):
            
        # Get color info directly within function to simplify the main code
            color_name, symbol = condition_mapping.get(grade, ("black", "?"))
            color_value = color_mapping.get(color_name, (0, 0, 0))
            
            text_instances = page.search_for(search_text)
            for inst in text_instances:
                rect = fitz.Rect(inst.x0, inst.y0, inst.x1, inst.y1)
                
                # Overwrite old text with white box
                page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1))
                
                # Insert new text with appropriate color
                page.insert_text(
                    (rect.x0, rect.y0 + 5),
                    f"{grade} {symbol}",
                    fontname="helv",
                    fontsize=10,
                    color=color_value
                )
        for page in doc:
        # Update both grades with simplified calls
            update_text_field(page, "r12", property['r12'])
            update_text_field(page, "r22", property['r22'])
            update_text_field(page, "r32", property['r32'])
            update_text_field(page, "r42", property['r42'])
            update_text_field(page, "r52", property['r52'])
            update_text_field(page, "r62", property['r62'])
            update_text_field(page, "r72", property['r72'])
            update_text_field(page, "r82", property['r82'])
            update_text_field(page, "r92", property['r92'])
            update_text_field(page, "r102", property['r102'])
            update_text_field(page, "kkeq23", property['r112'])
            
            
            
            update_text_field(page, "b12", property['b12'])
            update_text_field(page, "b22", property['b22'])
            update_text_field(page, "b32", property['b32'])
            update_text_field(page, "b42", property['b42'])
            update_text_field(page, "b52", property['b52'])
            update_text_field(page, "b62", property['b62'])
            
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            temp_pdf = tmp_file.name
            
            # Create the table PDF
            create_permit_table_with_reportlab(property['permitInformation'], temp_pdf)
            
            page_number = 6
            
            # Get the page
            page = doc[page_number]
            
            # You might need to adjust how you identify the table location
            search_text = "totalUnit"
            text_instances = page.search_for(search_text)
            
            page_width = page.rect.width
            page_height = page.rect.height
            
            # Calculate table position and dimensions
            table_x0 = 50  # Left margin
            
            # Position below the heading
            if text_instances and len(text_instances) > 0:
                table_rect = text_instances[0]
                table_y0 = table_rect.y0 - 30  # Position slightly above the header
            else:
                table_y0 = 80
            
            # Get the full width and enough height for the table
            table_width = page_width - (table_x0 * 2)
            table_height = page_height - table_y0 - 50
            print(page_height)
            # IMPORTANT: Create a larger white rectangle to fully cover the existing table
            # Make it wide enough and tall enough to cover everything
            page.draw_rect(
                fitz.Rect(0, table_y0 - 20, page_width, page_height), 
                color=(1, 1, 1),  # White color
                fill=(1, 1, 1),   # White fill
                width=0           # No border
            )
            
            # Open the temporary PDF with the table
            table_doc = fitz.open(temp_pdf)
            
            try:
                # Show the PDF page with full width and appropriate height
                page.show_pdf_page(
                    fitz.Rect(table_x0, table_y0, table_x0 + table_width, table_y0 + table_height),
                    table_doc,
                    0
                )
            except ValueError as e:
                # Alternative approach if there's an issue
                logging.error(f"Error showing PDF page: {str(e)}")
                src_rect = table_doc[0].rect
                page.show_pdf_page(
                    fitz.Rect(table_x0, table_y0, table_x0 + table_width, table_y0 + table_height),
                    table_doc,
                    0,
                    src_rect=src_rect
                )
            
            # Close the table document
            table_doc.close()
            
            # Clean up the temporary file
            os.unlink(temp_pdf)
    
        doc.save("/tmp/output2.pdf")
        doc.close()

            
            
            
            

    except:
        print(traceback.format_exc())
    try:
        s3_client = boto3.client('s3')
        s3_bucket_name = 'generate-report-fasst-python'
        s3_client.upload_file('/tmp/output3.pdf', s3_bucket_name, output_s3_key)
        logging.info(f"Successfully uploaded processed PDF to S3: {output_s3_key}")
    except ClientError as e:
        logging.error(f"Error uploading PDF to S3: {str(e)}")
        raise
    
    
    
def formate_data(property):
    formatted_data = {}
    
    # List of all fields to check
    fields = [
        ('addressKey', 'address'),
        ('heatedSpace', 'heatedSpace'),
        ('image_url', 'mediaUrl'),
        ('bedroom', 'beds'),
        ('bathroom', 'bathroom'),
        ('listPrice', 'askingPrice'),
        ('daysOnMarket', 'daysOnMarket'),
        ('yearInBuilt', 'year'),
        ('milestoneReportAvailable', 'milestoneReportAvailable'),
        ('currentFee', 'regularAssessmentAmount'),
        # ('potentialFee', 'askingPrice'),
        # ('monthlyInc', 'askingPricew'),
        # ('requiredReserve', 'askingPrice'),
        # ('shortfallAmount', 'askingPrice'),
        # ('currentReserve', 'askingPrice'),
          
    ]
    
    # Process each field
    for output_key, input_key in fields:
        value = property.get(input_key)
        formatted_data[output_key] = str(value) if value is not None and value != "" else ""
        
    milestone_ai_response = property.get('milestoneAiResponse', {})
    try:
        # Access the nested field
        phase2_report_needed = milestone_ai_response.get('phase2MilestoneReportNeeded')
        
        # Add to formatted data
        formatted_data['phase2Milestone'] = phase2_report_needed if phase2_report_needed is not None and phase2_report_needed != "" else ""
    except (AttributeError, TypeError):
        formatted_data['phase2Milestone'] = ""
        
    buildingElementData=property['buildingElementData']
    
    try:
        formatted_data['r11']=buildingElementData[0]['lifeRemaining']
        formatted_data['r21']=buildingElementData[1]['lifeRemaining']
        formatted_data['r31']=buildingElementData[2]['lifeRemaining']
        formatted_data['r41']=buildingElementData[3]['lifeRemaining']
        formatted_data['r51']=buildingElementData[4]['lifeRemaining']
        formatted_data['r61']=buildingElementData[5]['lifeRemaining']
        formatted_data['r71']=buildingElementData[6]['lifeRemaining']
        formatted_data['r81']=buildingElementData[7]['lifeRemaining']
        formatted_data['r91']=buildingElementData[8]['lifeRemaining']
        formatted_data['r101']=buildingElementData[9]['lifeRemaining']
        formatted_data['r111']=buildingElementData[10]['lifeRemaining']
        
        formatted_data['r12']=buildingElementData[0]['conditionGrade']
        formatted_data['r22']=buildingElementData[1]['conditionGrade']
        formatted_data['r32']=buildingElementData[2]['conditionGrade']
        formatted_data['r42']=buildingElementData[3]['conditionGrade']
        formatted_data['r52']=buildingElementData[4]['conditionGrade']
        formatted_data['r62']=buildingElementData[5]['conditionGrade']
        formatted_data['r72']=buildingElementData[6]['conditionGrade']
        formatted_data['r82']=buildingElementData[7]['conditionGrade']
        formatted_data['r92']=buildingElementData[8]['conditionGrade']
        formatted_data['r102']=buildingElementData[9]['conditionGrade']
        formatted_data['r112']=buildingElementData[10]['conditionGrade']
        
    except Exception as e:
        print(str(e))
        
    buildingServicesData=property['buildingServicesData']
    
    try:
        formatted_data['b11']=buildingServicesData[0]['lifeRemaining']
        formatted_data['b21']=buildingServicesData[1]['lifeRemaining']
        formatted_data['b31']=buildingServicesData[2]['lifeRemaining']
        formatted_data['b41']=buildingServicesData[3]['lifeRemaining']
        formatted_data['b51']=buildingServicesData[4]['lifeRemaining']
        formatted_data['b61']=buildingServicesData[5]['lifeRemaining']
        
        
    
        formatted_data['b12']=buildingServicesData[0]['conditionGrade']
        formatted_data['b22']=buildingServicesData[1]['conditionGrade']
        formatted_data['b32']=buildingServicesData[2]['conditionGrade']
        formatted_data['b42']=buildingServicesData[3]['conditionGrade']
        formatted_data['b52']=buildingServicesData[4]['conditionGrade']
        formatted_data['b62']=buildingServicesData[5]['conditionGrade']
    
    except Exception as e:
        print(str(e))
    try:
        formatted_data['permitInformation']=property['permitDetails']+property['entireAppartmnetPermitDetails']
    except Exception as e:
        formatted_data['permitInformation']=[]

        
        
    
    return formatted_data
    

def handler(event, context):
    client, db = connect_to_mongo()
    try:
        body = json.loads(event['body'])
        address = body['address']
        result = db.find_one({"address": address})

        if result:
            
                formatted_data=formate_data(result)
                # print(formatted_data)
                s3_bucket_name = 'generate-report-fasst-python'
                s3_object_key = 'FASST (9).pdf'
                output_s3_key = f'{address}.pdf'

                try:
                    s3_client = boto3.client('s3')
                    s3_response = s3_client.get_object(Bucket=s3_bucket_name, Key=s3_object_key)
                    pdf_content = s3_response['Body'].read()

                    

                    temp_file_path = '/tmp/temp_input.pdf'
                    with open(temp_file_path, 'wb') as f:
                        f.write(pdf_content)

                    generate_report(formatted_data,temp_file_path,output_s3_key)

                    os.remove(temp_file_path)

                    return {
                        'statusCode': 200,
                        'body': json.dumps({
                            'message': 'PDF processing successful',
                            'outputKey': output_s3_key,
                            'status': True
                        }),
                        "headers": common_headers,
                    }

                except (ClientError, NoCredentialsError) as e:
                    logging.error(f"AWS error: {str(e)}")
                    return {
                        'statusCode': 500,
                        'body': json.dumps({'error': str(e)}),
                        "headers": common_headers,
                    }


        else:
            logging.info(f"Address not found: {address}")
            return {
                'statusCode': 404,
                'body': json.dumps({"message": "Address not found in database"}),
                "headers": common_headers,
            }

    except Exception as e:
        logging.error(f"Error: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({'error': str(e)}),
            "headers": common_headers,
        }
    
# handler({
#     'address':'7920 SUN ISLAND DRIVE S UNIT 403'
# },0)