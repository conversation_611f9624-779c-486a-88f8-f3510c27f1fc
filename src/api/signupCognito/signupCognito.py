import json
import boto3
import os
import logging




# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

common_headers = {
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "OPTIONS,POST",
    }

def handler(event, context):
    cognito = boto3.client('cognito-idp')
    client_id = os.environ['USER_POOL_CLIENT_ID']
    

    
    try:
        
        logger.info("Received Event: %s", str(event))
        
        
        if 'body' in event:
            body = json.loads(event['body'])
        elif 'queryStringParameters' in event and event['queryStringParameters']:
            body = json.loads(event['queryStringParameters']['body'])
        else:
            raise ValueError("Unable to find body in event")

        
        logger.info("Parsed Body: %s", str(body))

        username = body.get('username')
        password = body.get('password')
        email = body.get('email')

        if not all([username, password, email]):
            raise ValueError("Missing required fields in body")

    
        response = cognito.sign_up(
            ClientId=client_id,
            Username=email,
            Password=password,
            UserAttributes=[
                {'Name': 'email', 'Value': email},
                {'Name': 'custom:Name', 'Value': username},
               
            ],
            ValidationData=[  # Optionally add validation data (e.g., email verification)
                {'Name': 'email', 'Value': email},
            ]
        )
        
        logger.info(f"User {username} signed up successfully.")
        
        return {
            'statusCode': 200,
            'body': json.dumps('User signed up successfully. Please check your email for verification.'),
             "headers": common_headers,
            
        }
    except KeyError as ke:
        logger.error(f"KeyError: {ke}")
        return {
            'statusCode': 400,
            'body': json.dumps(f'Missing required key: {str(ke)}'),
             "headers": common_headers,
            
        }
    except json.JSONDecodeError as jde:
        logger.error(f"JSON Decode Error: {jde}")
        return {
            'statusCode': 400,
            'body': json.dumps(f"Failed to decode JSON: {str(jde)}"),
             "headers": common_headers,
            
        }
    except ValueError as ve:
        logger.error(f"ValueError: {ve}")
        return {
            'statusCode': 400,
            'body': json.dumps(f"Invalid input: {str(ve)}"),
             "headers": common_headers,
            
        }
    except cognito.exceptions.UsernameExistsException:
        logger.error(f"Username {username} already exists.")
        return {
            'statusCode': 400,
            'body': json.dumps('Username already exists.'),
             "headers": common_headers,
            
        }
    except Exception as e:
        logger.error(f"Error signing up user: {str(e)}")
        return {
            'statusCode': 400,
            'body': json.dumps({"message":str(e)}),
             "headers": common_headers,
            
        }
