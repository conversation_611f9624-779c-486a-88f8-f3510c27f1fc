useDotenv: true
service: fasst-backend-${self:provider.stage}
frameworkVersion: '3'
provider:
  name: aws 
  region: ${env:REGION}
  runtime: python3.10
  stage: ${opt:stage, 'dev'}
  iam:
    role:
      name: fasst-backend-${self:provider.stage}
      statements:
        - Effect: Allow
          Action:
            - 'lambda:*'
            - 'iam:*'
            - 'cognito-idp:*'
            - 'ssm:GetParameter'
            - 'lambda:InvokeFunction'
            - 'logs:CreateLogGroup'
            - 'logs:CreateLogStream'
            - 'logs:PutLogEvents'
            - "ses:SendEmail"
            - "ses:SendRawEmail"
            - "ecr:*"
            - "s3:*"
            - "textract:*"
            - "sns:*"
            - "sqs:*"

          Resource: '*' # Allow actions on all resources
  environment:
    STAGE: ${self:provider.stage}
    MONGO_CONNECTION_URI: ${env:MONGO_CONNECTION_URI}
    MONGO_DB_NAME: ${env:MONGO_DB_NAME}
    EMAIL_FROM: ${env:SENDER_EMAIL}
    EMAIL_TO: ${env:RECIPIENT_EMAIL}
    APP_PASSWORD: ${env:APP_PASSWORD}
  
  ecr:
    images:
      generaterport:
        path: ./src/api/generateReport
        file: Dockerfile
        buildArgs:
          STAGE: latest
        cacheFrom:
          - generaterport:latest
      sirspdfgenai:
        path: ./src/api/sirsPdfGenAI
        file: Dockerfile
        buildArgs:
          STAGE: latest
        cacheFrom:
          - sirspdfgenai:latest
      pinellaspropertyappraiser:
        path: ./src/engine/pinellasPropertyAppraiser
        file: Dockerfile
        buildArgs:
          STAGE: latest
        cacheFrom:
          - pinellaspropertyappraiser:latest
      pinellaspermitscraper:
        path: ./src/engine/pinellasPermitScraper
        file: Dockerfile
        buildArgs:
          STAGE: latest
        cacheFrom:
          - pinellaspermitscraper:latest
      milestonepdfgenai:
        path: ./src/api/milestonePdfGenAi
        file: Dockerfile
        buildArgs:
          STAGE: latest
        cacheFrom:
          - milestonepdfgenai:latest

      pinellasdetailedscrapper:
        path: ./src/engine/pinellasDetailedScraper
        file: Dockerfile
        buildArgs:
          STAGE: latest
        cacheFrom:
          - pinellasdetailedscrapper:latest
    


custom:
  scripts:
    hooks:
      before:package:initialize: chmod +x ./installLayers.sh && ./installLayers.sh

  mongoUri: /creds/db/fasst/${self:provider.stage}/mongo_uri
  
layers:
  pymongoDependency:
    path: layers/pymongoDependency
    description: Lightweight dependencies for pymongo lambda
  PyJWTDependency:
    path: layers/PyJWTDependency
    description: Dependencies for PyJWTDependency
  globalDependency:
    path: layers/globalDependency
    description: Dependencies for globalDependency lambda (heavy packages)
  

 
  
  


  # pdfjsDependency:
  #   path: layers/pdfjsDependency
  #   description: Dependencies for pdfjsDependency lambda
  
  # awsdkDependency:
  #   path: layers/awsdkDependency
  #   description: Dependencies for awsdkDependency lambda

resources:
  Resources:
    MongoUri:
      Type: AWS::SSM::Parameter
      Properties:
        Name: ${self:custom.mongoUri}
        Type: String
        Value: ${env:MONGO_CONNECTION_URI}
        Description: Mongo Uri

    LambdaExecuteRole:
      Type: AWS::IAM::Role
      Properties:
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service:
                  - lambda.amazonaws.com
              Action:
                - sts:AssumeRole
        Path: /
        ManagedPolicyArns:
          - arn:aws:iam::aws:policy/CloudWatchFullAccess
          - arn:aws:iam::aws:policy/AmazonS3FullAccess
          - arn:aws:iam::aws:policy/AWSLambda_FullAccess
          - arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess
          - arn:aws:iam::aws:policy/AmazonSQSFullAccess

        Policies:
          - PolicyName: dev_EC2_metrics_monitor
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - sts:AssumeRole
                  Resource: '*'
    # generateReport:
    #   Type: AWS::IAM::Role
    #   Properties:
    #     RoleName: generateReport-${self:provider.stage}
    #     AssumeRolePolicyDocument:
    #       Version: "2012-10-17"
    #       Statement:
    #         - Effect: Allow
    #           Action: sts:AssumeRole
    #           Principal:
    #             Service: lambda.amazonaws.com
    #     Policies:
    #       - PolicyName: generateReport-${self:provider.stage}
    #         PolicyDocument:
    #           Version: "2012-10-17"
    #           Statement:
    #             - Effect: Allow
    #               Action:
    #                 - logs:CreateLogGroup
    #               Resource: "arn:aws:logs:us-east-1:953683027250:*"
    #             - Effect: Allow
    #               Action:
    #                 - logs:CreateLogStream
    #                 - logs:PutLogEvents
    #               Resource: "arn:aws:lambda:us-east-1:953683027250:function:generatePdfReport:*"
    #             - Effect: Allow
    #               Action:
    #                 - s3:GetObject
    #                 - s3:PutObject
    #                 - s3:ListBucket

    #               Resource: "arn:aws:s3:::generate-report-fasst-python/*"

    #             - Effect: Allow
    #               Action:
    #                 - ecr:GetDownloadUrlForLayer
    #                 - ecr:BatchCheckLayerAvailability
    #                 - ecr:BatchGetImage
    #               Resource: "arn:aws:ecr:us-east-1:953683027250:repository/serverless-fasst-backend-prod-prod"
  
    #     ManagedPolicyArns:
    #       - arn:aws:iam::aws:policy/AmazonS3FullAccess
    #       - arn:aws:iam::aws:policy/AmazonS3OutpostsFullAccess
    #       - arn:aws:iam::aws:policy/AmazonSQSFullAccess
    #       - arn:aws:iam::aws:policy/AmazonSSMFullAccess
    #       - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
    #       - arn:aws:iam::aws:policy/AmazonElasticContainerRegistryPublicFullAccess 

    # loginCognito:
    #   Type: AWS::IAM::Role
    #   Properties:
    #     RoleName: loginCognito-${self:provider.stage}
    #     AssumeRolePolicyDocument:
    #       Version: "2012-10-17"
    #       Statement:
    #         - Effect: Allow
    #           Action: sts:AssumeRole
    #           Principal:
    #             Service: lambda.amazonaws.com
    #     Policies:
    #       - PolicyName: loginCognito-${self:provider.stage}
    #         PolicyDocument:
    #           Version: "2012-10-17"
    #           Statement:
    #             - Effect: Allow
    #               Action:
    #                 - cognito-idp:AdminGetUser
    #                 - cognito-idp:InitiateAuth
    #               Resource:
    #                 - arn:aws:cognito-idp:us-east-1:953683027250:userpool/us-east-1_A8S9Rf2zv

    ApiGatewayAuthorizer: 
      Type: AWS::ApiGateway::Authorizer
      Properties: 
        Name: authorizer-${self:provider.stage}
        Type: COGNITO_USER_POOLS
        IdentitySource: method.request.header.Authorization
        RestApiId: 
          Ref: ApiGatewayRestApi
        ProviderARNs: 
          - Fn::GetAtt:
              - CognitoUserPool
              - Arn

    CognitoUserPool:
      Type: AWS::Cognito::UserPool
      Properties:
        UserPoolName: !Sub fasst-userpool-${self:provider.stage}
        # AliasAttributes:
        #   - phone_number
        #   - email
        AccountRecoverySetting:
          RecoveryMechanisms:
            - Name: verified_email
              Priority: 1
        AdminCreateUserConfig:
          AllowAdminCreateUserOnly: false
        MfaConfiguration: "OFF"
        AutoVerifiedAttributes:
          - email
        UsernameAttributes:
          - email
        Policies:
          PasswordPolicy:
            MinimumLength: 6
            RequireLowercase: False
            RequireNumbers: False
            RequireSymbols: False
            RequireUppercase: False
        Schema:
          - Name: email
            AttributeDataType: String
            Mutable: true
            Required: true
          - Name: Name
            AttributeDataType: String
            Mutable: true
        #   - Name: phone_number
        #     AttributeDataType: String
        #     Mutable: true
        #     Required: false
        VerificationMessageTemplate:
          DefaultEmailOption: CONFIRM_WITH_CODE
          # EmailMessage: !Sub ${file('./email_template/verification_email.html')}
          EmailSubject: "FASST Verification Code"
        # EmailConfiguration:
        #   ConfigurationSet: !Ref SESConfigSet
        #   EmailSendingAccount: DEVELOPER
        #   SourceArn: !Sub arn:aws:ses:${AWS::Region}:${AWS::AccountId}:identity/${self:custom.envfile.EMAIL_ID}

    CognitoUserPoolClient:
      Type: AWS::Cognito::UserPoolClient
      Properties:
        ClientName: !Sub AppClient-${self:provider.stage}
        UserPoolId: !Ref CognitoUserPool
        SupportedIdentityProviders:
          - COGNITO
        ExplicitAuthFlows:
          - ALLOW_ADMIN_USER_PASSWORD_AUTH
          - ALLOW_USER_PASSWORD_AUTH
          - ALLOW_REFRESH_TOKEN_AUTH
          - ALLOW_USER_SRP_AUTH
          - ALLOW_CUSTOM_AUTH
        GenerateSecret: false
        IdTokenValidity: 1413 # 23.55 Hours
        AccessTokenValidity: 30
        RefreshTokenValidity: 1
        TokenValidityUnits:
          AccessToken: "minutes"
          IdToken: "minutes"
          RefreshToken: "days"
    SNSTopicRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: sns-lambda-role-fasst-${self:provider.stage}
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service: textract.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: sns-lambda-policy
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - lambda:InvokeFunction
                    - sns:*
                  Resource: '*'
    TextractSNSTopic:
      Type: AWS::SNS::Topic
      Properties:
        TopicName: textract-sns-notification-fasst-${self:provider.stage}
        DisplayName: textract-sns-notification
    
    PinellasPropertyAppraiserDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: PinellasPropertyAppraiserDLQ-${self:provider.stage}

    PinellasPropertyAppraiserQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: "PinellasPropertyAppraiserQueue-${self:provider.stage}"
        MaximumMessageSize: 262144
        MessageRetentionPeriod: 1209600
        ReceiveMessageWaitTimeSeconds: 20
        VisibilityTimeout: 1500
        RedrivePolicy:
          deadLetterTargetArn: !GetAtt PinellasPropertyAppraiserDLQ.Arn
          maxReceiveCount: 1

    PinellasPermitScraperDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: PinellasPermitScraperDLQ-${self:provider.stage}

    PinellasPermitScraperQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: "PinellasPermitScraperQueue-${self:provider.stage}"
        MaximumMessageSize: 262144
        MessageRetentionPeriod: 1209600
        ReceiveMessageWaitTimeSeconds: 20
        VisibilityTimeout: 1500
        RedrivePolicy:
          deadLetterTargetArn: !GetAtt PinellasPermitScraperDLQ.Arn
          maxReceiveCount: 1


    PinellasDetailedScraperQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: PinellasDetailedScraperQueueDLQ-${self:provider.stage}

    PinellasDetailedScraperQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: "PinellasDetailedScraperQueue-${self:provider.stage}"
        MaximumMessageSize: 262144
        MessageRetentionPeriod: 1209600
        ReceiveMessageWaitTimeSeconds: 20
        VisibilityTimeout: 1500
        RedrivePolicy:
          deadLetterTargetArn: !GetAtt PinellasDetailedScraperQueueDLQ.Arn
          maxReceiveCount: 1
functions:
  getProperties:
    name: getProperties-${self:provider.stage}
    handler: src/api/propertyList/propertyList.handler

    package:
      individually: true
      patterns:
        - '!src/**'
        - src/api/propertyList/**
        - src/helper/**
    events:
      - http:
          method: get 
          path: /getProperties
          cors: true
    layers:
      - !Ref PymongoDependencyLambdaLayer
    timeout: 30
    memorySize: 256

  updateProperty:
    name: updateProperty-${self:provider.stage}
    handler: src/api/updateProperty/updateProperty.handler
    package:
      individually: true
      patterns:
        - '!src/**'
        - src/api/updateProperty/**
        - src/helper/**
    events:
      - http:
          method: post 
          path: /updateProperty
          cors: true
          authorizer: null
    layers:
      - !Ref PymongoDependencyLambdaLayer
    timeout: 30
    memorySize: 256

  updateReportKey:
    name: updateReportKey-${self:provider.stage}
    handler: src/api/updateReportKey/updateReportKey.handler
    package:
      individually: true
      patterns:
        - '!src/**'
        - src/api/updateReportKey/**
        - src/helper/**
    events:
      - http:
          method: post 
          path: /updateReportKey
          cors: true
          authorizer: null
    layers:
      - !Ref PymongoDependencyLambdaLayer
    timeout: 30
    memorySize: 256
  
  uploadFile:
    name: uploadFile-${self:provider.stage}
    handler: src/api/uploadFile/uploadFile.handler
    package:
      individually: true
      patterns:
        - '!src/**'
        - src/api/uploadFile/**
        - src/helper/**
    events:
      - http:
          method: post 
          path: /uploadFile
          cors: true
          authorizer: null
    layers:
      - !Ref PymongoDependencyLambdaLayer
    timeout: 30
    memorySize: 256

  checkGenStatus:
    name: checkGenStatus-${self:provider.stage}
    handler: src/api/checkGenStatus/checkGenStatus.lambda_handler
    package:
      individually: true
      patterns:
        - '!src/**'
        - src/api/checkGenStatus/**
        - src/helper/**

    events:
      - http:
          method: post
          path: /checkGenStatus
          cors: true
   
    
    timeout: 30
    memorySize: 256  
    layers:
      - !Ref PymongoDependencyLambdaLayer

# Authentication for admin team
  adminLogin:
    name: adminLogin-${self:provider.stage}
    handler: src/api/adminLogin/adminLogin.handler
    package:
      individually: true
      patterns:
        - '!node_modules/**'
        - '!layers/**'
        - '!.git/**'
        - '!*.yml'
        - '!*.json'
        - '!*.sh'
        - '!buildspec.yml'
        - 'src/api/adminLogin/**'
        - 'src/helper/**'
    events:
      - http:
          path: adminLogin
          method: post
          cors: true
    layers:
      - !Ref PymongoDependencyLambdaLayer
      - !Ref PyJWTDependencyLambdaLayer


  NotificationSystem:
    name: NotificationSystem-${self:provider.stage}
    handler: src/NotificationSystem/sendNotifications.handler
    package:
      individually: true
      patterns:
        - '!node_modules/**'
        - '!layers/**'
        - '!.git/**'
        - '!*.yml'
        - '!*.json'
        - '!*.sh'
        - '!buildspec.yml'
        - 'src/NotificationSystem/**'
        - 'src/helper/**'
        
    events:
      - schedule:
          rate: cron(0 9 * * ? *)  # 9:00 AM UTC
      - schedule:
          rate: cron(0 16 * * ? *) # 4:00 PM UTC
    layers:
      - !Ref PymongoDependencyLambdaLayer
    
    timeout: 30
    memorySize: 256
  TextractCall:
    name: textractCall-fasst-${self:provider.stage}
    handler: src/engine/textractCall/callDetails.handler
    layers:
      - !Ref PymongoDependencyLambdaLayer

    timeout: 480
    logs: false
    package:
      individually: true
      patterns:
        - '!node_modules/**'
        - '!layers/**'
        - '!.git/**'
        - '!*.yml'
        - '!*.json'
        - '!*.sh'
        - '!buildspec.yml'
        - 'src/engine/textractCall/**'
        - 'src/helper/**'

    environment:
      ENV_SNS_TOPIC_ROLE_ARN: !GetAtt SNSTopicRole.Arn
      ENV_TOPIC_ARN: !Ref TextractSNSTopic
  getOcrResultsViaSns:
    name: getOcrResultsViaSns-fasst-${self:provider.stage}
    handler: src/engine/textractReceive/snsTrigger.handler
    maximumRetryAttempts: 2
    logs: false
    events:
      - sns:
          arn: !Ref TextractSNSTopic
          topicName: textract-sns-notification
    package:
      individually: true
      patterns:
        - '!node_modules/**'
        - '!layers/**'
        - '!.git/**'
        - '!*.yml'
        - '!*.json'
        - '!*.sh'
        - '!buildspec.yml'
        - 'src/engine/textractReceive/**'

    layers:
      - !Ref PymongoDependencyLambdaLayer
    environment:
      STAGE: ${self:provider.stage}

    timeout: 900
    memorySize: 512
  generatePdfReport:
    image:
      name: generaterport
    events:
      - http:
          method: post
          path: generateReport
          cors: true
          # authorizer: 
          #    type: COGNITO_USER_POOLS
          #    authorizerId: 
          #      Ref: ApiGatewayAuthorizer
    timeout: 60
    memorySize: 1024

  sirsPdfGenAi:
    name: sirsPdfGenAi-${self:provider.stage}
    image:
      name: sirspdfgenai
    timeout: 900
    memorySize: 1024

  pdfGenTrigger:
    name: pdfGenTrigger-${self:provider.stage}
    handler: src/api/pdfGenTrigger/pdfGenTrigger.lambda_handler
    package:
      individually: true
      patterns:
        - '!node_modules/**'
        - '!layers/**'
        - '!.git/**'
        - '!*.yml'
        - '!*.json'
        - '!*.sh'
        - '!buildspec.yml'
        - 'src/api/pdfGenTrigger/**'
        - 'src/helper/**'

    events:
      - http:
          method: post
          path: /pdfGenTrigger
          cors: true

    layers:
      - !Ref PymongoDependencyLambdaLayer
    environment:
      STAGE: ${self:provider.stage}  
      TARGET_FUNCTION_NAME: ${self:service}   
    timeout: 30
    memorySize: 256


  
  addresses:
    name: addresses-${self:provider.stage}
    handler: src/api/addresses/addresses.handler
    package:
      individually: true
      patterns:
        - '!node_modules/**'
        - '!layers/**'
        - '!.git/**'
        - '!*.yml'
        - '!*.json'
        - '!*.sh'
        - '!buildspec.yml'
        - 'src/api/addresses/**'
        - 'src/helper/**'
    layers:
        - !Ref PymongoDependencyLambdaLayer

    events:
      - http:
          method: post
          path: /addresses
          authorizer: 
             type: COGNITO_USER_POOLS
             authorizerId: 
               Ref: ApiGatewayAuthorizer
            
          cors: true
    
    timeout: 30
    memorySize: 256
  
  pdfUrl:
    name: pdfUrl-${self:provider.stage}
    handler: src/api/pdfUrl/pdfUrl.handler
    package:
      individually: true
      patterns:
        - '!node_modules/**'
        - '!layers/**'
        - '!.git/**'
        - '!*.yml'
        - '!*.json'
        - '!*.sh'
        - '!buildspec.yml'
        - 'src/api/pdfUrl/**'
        - 'src/helper/**'

    events:
      - http:
          method: post
          path: /pdfUrl
          authorizer: 
            type: COGNITO_USER_POOLS
            authorizerId: 
              Ref: ApiGatewayAuthorizer
          cors: true
    
    timeout: 30
    memorySize: 256
  
  signupCognito:
    name: signupCognito-${self:provider.stage}
    handler: src/api/signupCognito/signupCognito.handler
    package:
      individually: true
      patterns:
        - '!node_modules/**'
        - '!layers/**'
        - '!.git/**'
        - '!*.yml'
        - '!*.json'
        - '!*.sh'
        - '!buildspec.yml'
        - 'src/api/signupCognito/**'
        - 'src/helper/**'

    events:
      - http:
          method: post
          path: /signupCognito
          # type: COGNITO_USER_POOLS
          #    auauthorizer: 
          #    thorizerId: 
          #      Ref: ApiGatewayAuthorizer
            
          cors: true
    environment:
      USER_POOL_ID: 
        Ref: CognitoUserPool
      USER_POOL_CLIENT_ID:
        Ref: CognitoUserPoolClient
    
    timeout: 30
    memorySize: 256

  loginCognito:
    name: loginCognito-${self:provider.stage}
    handler: src/api/loginCognito/loginCognito.handler
    package:
      individually: true
      patterns:
        - '!node_modules/**'
        - '!layers/**'
        - '!.git/**'
        - '!*.yml'
        - '!*.json'
        - '!*.sh'
        - '!buildspec.yml'
        - 'src/api/loginCognito/**'
        - 'src/helper/**'

    events:
      - http:
          method: post
          path: /loginCognito
          # type: COGNITO_USER_POOLS
          #    auauthorizer: 
          #    thorizerId: 
          #      Ref: ApiGatewayAuthorizer
            
          cors: true
    # role: 
    #   Fn::GetAtt: [loginCognito, Arn]
    
    timeout: 30
    memorySize: 256
    environment:
      USER_POOL_ID: 
        Ref: CognitoUserPool
      USER_POOL_CLIENT_ID:
        Ref: CognitoUserPoolClient
  
  verifiyEmail:
    name: verifiyEmail-${self:provider.stage}
    handler: src/api/verifiyEmail/verifiyEmail.handler
    package:
      individually: true
      patterns:
        - '!node_modules/**'
        - '!layers/**'
        - '!.git/**'
        - '!*.yml'
        - '!*.json'
        - '!*.sh'
        - '!buildspec.yml'
        - 'src/api/verifiyEmail/**'
        - 'src/helper/**'

    events:
      - http:
          method: post
          path: /verifiyEmail
          # type: COGNITO_USER_POOLS
          #    auauthorizer: 
          #    thorizerId: 
          #      Ref: ApiGatewayAuthorizer
            
          cors: true
    
    timeout: 30
    memorySize: 256
    environment:
      USER_POOL_ID: 
        Ref: CognitoUserPool
      USER_POOL_CLIENT_ID:
        Ref: CognitoUserPoolClient
    layers:
        - !Ref PymongoDependencyLambdaLayer

  textractTest:
    name: textractTest-${self:provider.stage}
    handler: src/engine/textractTest/textractTest.lambda_handler
    package:
      individually: true
      patterns:
        - '!node_modules/**'
        - '!layers/**'
        - '!.git/**'
        - '!*.yml'
        - '!*.json'
        - '!*.sh'
        - '!buildspec.yml'
        - 'src/engine/textractTest/**'
        - 'src/helper/**'

    
    timeout: 100
    memorySize: 256
  fasstDataCrawl:
    name: fasstDataCrawl-${self:provider.stage}
    # events:
    #   - schedule:
    #       rate: cron(0/15 * * * ? *)
    events:
      - schedule:
          name: fasstDataCrawl-${self:provider.stage}
          description: This function  trigger every 1 hour, and return new properties
          rate: rate(1 hour)
          enabled: true
    handler: src/engine/fasstDataCrawl/fasstDataCrawl.handler
    package:
      individually: true
      patterns:
        - '!node_modules/**'
        - '!layers/**'
        - '!.git/**'
        - '!*.yml'
        - '!*.json'
        - '!*.sh'
        - '!buildspec.yml'
        - 'src/engine/fasstDataCrawl/**'
        - 'src/helper/**'

    timeout: 60
    memorySize: 512
    environment:
      PINELLAS_PROPERTY_APPRAISER_QUEUE_URL:
        Ref: PinellasPropertyAppraiserQueue
      PINELLAS_PERMIT_SCRAPER_QUEUE_URL:
        Ref: PinellasPermitScraperQueue
      PINELLAS_DETAILED_SCRAPPER_URL:
        Ref: PinellasDetailedScraperQueue
    layers:
        - !Ref GlobalDependencyLambdaLayer
  fasstWorkBookExcel:
    name: fasstWorkBook-${self:provider.stage}
    handler: src/engine/workBook/fasstWorkBook.handler
    layers:
      - !Ref GlobalDependencyLambdaLayer
    timeout: 900
    memorySize: 256
    
    package:
      individually: true
      patterns:
        - '!node_modules/**'
        - '!layers/**'
        - '!.git/**'
        - '!*.yml'
        - '!*.json'
        - '!*.sh'
        - '!buildspec.yml'
        - 'src/engine/workBook/**'
        - 'src/helper/**'

    description: This function generates a daily crawled property Excel sheet every day
    events:
      - schedule:
          name: fasstPreviousDay-${self:provider.stage}
          description: This function  trigger 12:30 PM IST every day
          rate: cron(30 6 * * ? *) # 12:00 IST every day
          enabled: true
          input:
            day: previousDay
      - schedule:
          name: fasstCurrent-${self:provider.stage}
          description: This function  trigger 12:35 PM IST every day
          rate: cron(35 6 * * ? *) # 12:05 IST every day
          enabled: true
          input:
            day: currentDay
  PinellasPropertyAppraiser:
    name: pinellasPropertyAppraiser-${self:provider.stage}
    image:
      name: pinellaspropertyappraiser
    events:
      - sqs:
          arn:
            Fn::GetAtt: [ PinellasPropertyAppraiserQueue, Arn ]
          batchSize: 1
    timeout: 900
    memorySize: 512

  PinellasDetailedPropertyAppraiser:
    name: pinellasDetailedPropertyAppraiser-${self:provider.stage}
    image:
      name: pinellasdetailedscrapper
    events:
      - sqs:
          arn:
            Fn::GetAtt: [ PinellasDetailedScraperQueue, Arn ]
          batchSize: 1
    timeout: 900
    memorySize: 512

  PinellasPermitScraper:
    name: pinellasPermitScraper-${self:provider.stage}
    image:
      name: pinellaspermitscraper
    events:
      - sqs:
          arn:
            Fn::GetAtt: [ PinellasPermitScraperQueue, Arn ]
          batchSize: 1
    timeout: 900
    memorySize: 512
  milestonePdfGenAi:
    name: milestonePdfGenAi-${self:provider.stage}
    image:
      name: milestonepdfgenai
    timeout: 100
    memorySize: 1024




plugins:
  - serverless-plugin-scripts

#   - serverless-better-credentials 
#   - serverless-plugin-scripts
#   - serverless-delete-loggroups
#   - serverless-offline
