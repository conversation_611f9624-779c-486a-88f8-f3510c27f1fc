#!/bin/bash

# List of dependencies with versions
dependencies=("pymongo==4.4.0" "PyJWT")


# Function to create a directory and a requirements.txt file inside

# Function to create a directory and a requirements.txt file inside
create_dependency_directory() {
    dependency="$1"
    directory="${dependency%%==*}Dependency"  # Extract dependency name without version
    
    # Create the directory if it doesn't exist
    mkdir -p "$directory"
    
    # Create the requirements.txt file and write the dependency with its version
    echo "$dependency" > "$directory/${directory}-requirements.txt"
}

# Create directories and requirements.txt files


for dep in "${dependencies[@]}"; do
    create_dependency_directory "$dep"
done